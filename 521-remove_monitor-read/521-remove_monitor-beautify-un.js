/*
 * VSCode Augment 插件反混淆分析
 *
 * 这是一个VSCode的Augment AI编程助手插件的核心文件
 * 经过混淆压缩，以下是反混淆分析结果
 */

// ============================================================================
// 核心工具函数和模块系统
// ============================================================================

// 对象创建和属性操作的简化引用
var createObject = Object.create,                    // zOt -> createObject
    defineProperty = Object.defineProperty,          // PB -> defineProperty
    getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor, // MOt -> getOwnPropertyDescriptor
    getOwnPropertyNames = Object.getOwnPropertyNames,           // FOt -> getOwnPropertyNames
    getPrototypeOf = Object.getPrototypeOf,                    // NOt -> getPrototypeOf
    hasOwnProperty = Object.prototype.hasOwnProperty,          // YOt -> hasOwnProperty

    // 模块缓存和延迟加载系统
    lazyEvaluator = (moduleFunc, cachedResult) => () => cachedResult = moduleFunc ? moduleFunc(moduleFunc = 0) : cachedResult, // ec -> lazyEvaluator

    // CommonJS模块包装器 - 创建模块导出对象
    moduleWrapper = (moduleFunc, exports) => () => (exports || moduleFunc((exports = {exports: {}}).exports, exports), exports.exports), // O -> moduleWrapper

    // 属性批量定义工具
    defineProperties = (target, properties) => {     // Yy -> defineProperties
        for (var key in properties) defineProperty(target, key, {
            get: properties[key],
            enumerable: true
        })
    },

    // 对象属性复制工具 - 用于模块导入/导出
    copyProperties = (target, source, excludeKey, descriptor) => {  // P7e -> copyProperties
        if (source && "object" == typeof source || "function" == typeof source)
            for (let key of getOwnPropertyNames(source))
                hasOwnProperty.call(target, key) || key === excludeKey ||
                defineProperty(target, key, {
                    get: () => source[key],
                    enumerable: !(descriptor = getOwnPropertyDescriptor(source, key)) || descriptor.enumerable
                });
        return target
    },

    // ES模块导入包装器
    moduleImporter = (module, isDefault, result) => (  // me -> moduleImporter
        result = null != module ? createObject(getPrototypeOf(module)) : {},
        copyProperties(!isDefault && module && module.__esModule ? result : defineProperty(result, "default", {
            value: module,
            enumerable: true
        }), module)
    ),

    // ES模块导出标记
    markAsESModule = module => copyProperties(defineProperty({}, "__esModule", {  // tc -> markAsESModule
        value: true
    }), module);

// ============================================================================
// Lodash工具函数模块
// ============================================================================

// 检查是否为对象类型
var isObject = moduleWrapper((exports, module) => {  // Eh -> isObject
    module.exports = function(value) {
        var type = typeof value;
        return null != value && ("object" == type || "function" == type)
    }
});

// 全局对象检测
var getGlobalObject = moduleWrapper((exports, module) => {  // Mre -> getGlobalObject
    var globalRef = "object" == typeof global && global && global.Object === Object && global;
    module.exports = globalRef
});

// 根对象获取 (global/self/window)
var getRootObject = moduleWrapper((exports, module) => {  // mp -> getRootObject
    var globalObj = getGlobalObject(),
        selfObj = "object" == typeof self && self && self.Object === Object && self,
        root = globalObj || selfObj || Function("return this")();
    module.exports = root
});

// 当前时间戳获取器
var getCurrentTimestamp = moduleWrapper((exports, module) => {  // owe -> getCurrentTimestamp
    var root = getRootObject();
    module.exports = function() {
        return root.Date.now()
    }
});

// 字符串尾部空白字符处理
var trimEndWhitespace = moduleWrapper((exports, module) => {  // lwe -> trimEndWhitespace
    var whitespaceRegex = /\s/;
    module.exports = function(string) {
        for (var index = string.length; index-- && whitespaceRegex.test(string.charAt(index)););
        return index
    }
});

// 字符串去除首尾空白
var trimString = moduleWrapper((exports, module) => {  // dwe -> trimString
    var trimEnd = trimEndWhitespace(),
        leadingWhitespace = /^\s+/;
    module.exports = function(string) {
        return string && string.slice(0, trimEnd(string) + 1).replace(leadingWhitespace, "")
    }
});

// Symbol引用获取
var getSymbol = moduleWrapper((exports, module) => {  // Uy -> getSymbol
    var symbolRef = getRootObject().Symbol;
    module.exports = symbolRef
});

// ============================================================================
// 类型检测和转换工具
// ============================================================================

// 获取对象的字符串标签 (用于类型检测)
var getObjectTag = moduleWrapper((exports, module) => {  // mwe -> getObjectTag
    var Symbol = getSymbol(),
        objectProto = Object.prototype,
        hasOwnProp = objectProto.hasOwnProperty,
        toString = objectProto.toString,
        toStringTag = Symbol ? Symbol.toStringTag : void 0;

    module.exports = function(value) {
        var isOwn = hasOwnProp.call(value, toStringTag),
            tag = value[toStringTag];
        try {
            var unmasked = !(value[toStringTag] = void 0)
        } catch {}
        var result = toString.call(value);
        return unmasked && (isOwn ? value[toStringTag] = tag : delete value[toStringTag]), result
    }
});

// 原生toString方法
var nativeToString = moduleWrapper((exports, module) => {  // Awe -> nativeToString
    var toString = Object.prototype.toString;
    module.exports = function(value) {
        return toString.call(value)
    }
});

// 基础类型标签获取器
var getBaseTag = moduleWrapper((exports, module) => {  // SA -> getBaseTag
    var Symbol = getSymbol(),
        getTag = getObjectTag(),
        nativeObjectToString = nativeToString(),
        toStringTag = Symbol ? Symbol.toStringTag : void 0;

    module.exports = function(value) {
        return null == value ?
            void 0 === value ? "[object Undefined]" : "[object Null]" :
            (toStringTag && toStringTag in Object(value) ? getTag : nativeObjectToString)(value)
    }
});

// ============================================================================
// 插件主要功能模块
// ============================================================================

/*
 * 从代码结构分析，这个插件主要包含以下核心功能：
 *
 * 1. AI代码补全和建议 (Augment AI completion)
 * 2. 聊天界面和交互 (Chat interface)
 * 3. 工作区管理 (Workspace management)
 * 4. 用户认证和会话管理 (Authentication & session)
 * 5. 配置管理 (Configuration management)
 * 6. 特性标志管理 (Feature flags)
 * 7. 文件编辑和同步 (File editing & sync)
 * 8. 模型管理 (AI model management)
 * 9. 调试和诊断功能 (Debug & diagnostics)
 * 10. 扩展生命周期管理 (Extension lifecycle)
 */

// 从文件末尾可以看到主要的激活函数和配置同步
// 主激活函数: qun (应该是 "activate" 的混淆名)
// 配置同步函数: fkt (应该是 "setupContextKeySync" 的混淆名)

// ============================================================================
// 关键配置项和上下文键
// ============================================================================

/*
 * 从代码中识别出的重要配置项：
 *
 * - vscode-augment.enableDebugFeatures: 启用调试功能
 * - vscode-augment.enableReviewerWorkflows: 启用审查工作流
 * - vscode-augment.enableNextEdit: 启用下一步编辑功能
 * - vscode-augment.enableGenerateCommitMessage: 启用生成提交消息
 * - vscode-augment.workspace-manager-ui.enabled: 工作区管理UI
 * - vscode-augment.sources-enabled: 启用源码功能
 * - vscode-augment.chat-hint.decoration: 聊天提示装饰
 * - vscode-augment.cpu-profile.enabled: CPU性能分析
 * - vscode-augment.featureFlags.enableRemoteAgents: 远程代理
 */

// ============================================================================
// URI处理和命令路由
// ============================================================================

/*
 * 插件支持的URI路径：
 *
 * - /auth/mcp: MCP OAuth回调处理
 * - S.authRedirectURI.path: 认证重定向
 * - zte.openChat: 打开聊天界面
 * - zte.openAugmentSettings: 打开Augment设置
 * - zte.openGuidelinesSettings: 打开指导原则设置
 * - zte.openMemories: 打开记忆文件
 */

// ============================================================================
// 核心类和主要功能模块分析
// ============================================================================

/*
 * 通过分析代码结构，发现这个插件的主要类和功能模块：
 */

// 主要的类定义 (从class关键字识别)
// 1. PS - 路径迭代器类，用于遍历文件系统路径
// 2. p8e - 不支持的文件扩展名错误类
// 3. nte - 文件扩展名过滤器基类
// 4. f8e - 带忽略路径映射的文件过滤器
// 5. h8e - 带忽略栈的文件过滤器
// 6. ite - 序列号生成器类
// 7. m8e - Blob名称计算器类
// 8. _8e - 文件变更监听器类
// 9. A8e - 文件上传处理器类
// 10. b8e - 可上传文件判断器类

// 工作区和版本控制相关类
// 11. cte - Git操作类，处理工作目录变更
// 12. lte - VCS仓库监听器，监控版本控制系统变更
// 13. ute - 仓库监听器管理器
// 14. dte - 开放文件管理器代理

// 数据存储和缓存相关类
// 15. pte - 简单键值存储类
// 16. fte - Blob状态存储类
// 17. C8e - 开放文档快照缓存类
// 18. hte - 文件编辑处理器类
// 19. x8e - 工作区资源管理器
// 20. k8e - 文件变更大小计数器

// 文件管理和同步相关类
// 21. mte - 开放文件管理器V2
// 22. KW - 工作队列项失败错误类
// 23. T8e - 错误抛出器类
// 24. R8e - 进度报告器类
// 25. D8e - 单项工作队列项
// 26. L8e - 批量工作队列项
// 27. _te - 基础队列类
// 28. O8e - 单项处理队列
// 29. W8e - 批量处理队列
// 30. Ate - 工作队列管理器

// 文件系统和路径管理
// 31. bte - 检查点管理器
// 32. B8e - 限制容器类
// 33. z8e - 项目计数限制器
// 34. vte - 磁盘文件管理器
// 35. T3 - 缓存文件名常量类
// 36. M8e - 修改时间缓存条目
// 37. yte - 修改时间缓存管理器

// 变更跟踪和文档管理
// 38. F8e - 文件修改记录类
// 39. QS - 变更序列类
// 40. qS - 变更跟踪器类
// 41. wte - 跟踪文档基类
// 42. N8e - 文本文档跟踪类
// 43. Y8e - 笔记本文档跟踪类
// 44. Cte - 开放文件管理器V1

// 路径和文件分类
// 45. Ste - 文件分类器类
// 46. kte - 路径映射管理器
// 47. P8e - 路径信息管理器
// 48. $8e - 路径错误类
// 49. Ite - 路径通知器类

// 工作区和同步管理
// 50. Tte - 文件验证器类
// 51. Rte - 固定大小列表类
// 52. Dte - 标签页监听器类
// 53. Lte - 未知Blob处理器类
// 54. Ote - 查看内容跟踪器类

// 核心工作区管理
// 55. US - 工作区状态类
// 56. V8e - 文件夹资源管理器
// 57. H8e - 路径过滤器基类
// 58. Wte - 工作区管理器主类 (核心类)
// 59. Bte - 动作模型状态管理器

// 主要的扩展类
// 60. U9 - 主扩展类 (核心扩展管理器)

// ============================================================================
// 主要功能函数分析
// ============================================================================

/*
 * 关键功能函数：
 */

// 工具函数
// - mx(): 生成随机字节数组，用于UUID生成
// - aBt(): UUID验证函数
// - kA(): UUID格式化函数
// - oBt(): UUID字符串化验证函数
// - cBt(): UUID v1生成函数
// - lBt(): UUID解析函数
// - uBt(): 字符串转字节数组
// - bx(): UUID命名空间处理函数
// - fBt(): MD5哈希函数
// - gBt(): UUID v4生成函数
// - mBt(): SHA1哈希函数
// - ABt(): UUID版本获取函数

// 类型系统和装饰器
// - FCe(): 类继承扩展函数
// - NCe(): 对象属性排除函数
// - YCe(): 装饰器应用函数
// - PCe(): 参数装饰器函数
// - RBt(): ES装饰器函数
// - DBt(): 初始化器运行函数
// - LBt(): 属性键处理函数
// - OBt(): 函数名设置函数

// 异步和生成器
// - $Ce(): 元数据装饰器函数
// - QCe(): 异步等待器函数
// - qCe(): 生成器函数
// - UCe(): 导出星号函数
// - pz(): 值迭代器函数
// - lne(): 读取函数
// - VCe(): 展开函数
// - HCe(): 展开数组函数
// - GCe(): 展开数组函数
// - Jy(): 等待包装器函数
// - jCe(): 异步生成器函数
// - KCe(): 异步委托器函数
// - JCe(): 异步值函数
// - XCe(): 模板对象创建函数
// - ZCe(): 导入星号函数
// - eSe(): 默认导入函数

// 私有成员访问
// - tSe(): 私有字段获取函数
// - rSe(): 私有字段设置函数
// - nSe(): 私有字段检查函数
// - sSe(): 可释放资源添加函数
// - iSe(): 资源释放函数

// 文件系统操作
// - Sy(): 事件等待函数
// - Eun(): 对象值查找函数
// - wun(): 差异变更类型获取函数
// - Cun(): 变更类型映射函数
// - Sun(): 删除路径处理函数
// - xun(): 添加路径处理函数
// - ote(): Git差异解析函数

// 路径和文件处理
// - Gxt(): 文件管理器版本检查函数
// - gte(): 文档文本获取函数
// - S8e(): 事件文档获取函数
// - $S(): 数组过滤函数
// - lA(): 日志输出函数
// - Dun(): 修改时间条目验证函数
// - Lun(): 缓存文件名生成函数
// - Ete(): 缓存存在检查函数
// - Jxt(): 缓存移动函数
// - Xxt(): 缓存读取函数

// 文档和变更跟踪
// - Yun(): 笔记本检查函数
// - Pun(): 文档包装函数
// - U8e(): URI路径获取函数
// - tB(): 文件URI路径获取函数
// - rB(): 同步阻塞检查函数
// - akt(): 嵌套检查函数
// - np(): 文件夹状态获取函数
// - skt(): 路径扫描函数

// 主要入口函数
// - j8e(): 会话ID获取/生成函数
// - qun(): 扩展激活主函数 (插件入口点)
// - ckt(): 上下文键设置函数
// - fkt(): 上下文键同步设置函数

// ============================================================================
// 插件架构和工作流程分析
// ============================================================================

/*
 * 插件的整体架构：
 *
 * 1. 扩展激活流程 (qun函数)：
 *    - 初始化日志记录器
 *    - 设置扩展启用/禁用处理器
 *    - 注册URI处理器 (OAuth回调、聊天打开等)
 *    - 创建核心服务实例
 *
 * 2. 核心服务组件：
 *    - 认证管理 (OAuth/API Token)
 *    - 配置管理 (用户设置)
 *    - 工作区管理 (文件同步、版本控制)
 *    - AI服务 (代码补全、聊天)
 *    - 特性标志管理
 *    - 事件报告和指标收集
 *
 * 3. 文件系统集成：
 *    - 文件监听和变更跟踪
 *    - 文件上传和同步
 *    - 路径过滤和忽略规则
 *    - Git集成和版本控制
 *
 * 4. UI集成：
 *    - WebView面板 (聊天界面)
 *    - 状态栏显示
 *    - 命令注册
 *    - 上下文菜单
 *
 * 5. AI功能：
 *    - 代码补全建议
 *    - 智能聊天对话
 *    - 代码编辑建议
 *    - 提交消息生成
 */

// ============================================================================
// 重要配置项和特性标志
// ============================================================================

/*
 * 从代码中提取的重要配置项：
 *
 * 基础功能配置：
 * - enableDebugFeatures: 启用调试功能
 * - enableReviewerWorkflows: 启用代码审查工作流
 * - enableUpload: 启用文件上传功能
 * - modelName: AI模型名称配置
 * - completionURL: 代码补全服务URL
 * - apiToken: API访问令牌
 * - oauth: OAuth认证配置
 *
 * 高级功能配置：
 * - enableNextEdit: 启用下一步编辑功能
 * - enableGenerateCommitMessage: 启用提交消息生成
 * - enableSmartPaste: 启用智能粘贴
 * - enableInstructions: 启用指令功能
 * - enableWorkspaceManagerUi: 启用工作区管理UI
 *
 * 同步和上传配置：
 * - maxUploadSizeBytes: 最大上传文件大小
 * - maxTrackableFiles: 最大可跟踪文件数
 * - enableFileLimitsForSyncingPermission: 启用同步权限文件限制
 * - refuseToSyncHomeDirectories: 拒绝同步主目录
 * - verifyFolderIsSourceRepo: 验证文件夹是源代码仓库
 *
 * 性能和限制配置：
 * - vscodeEnableCpuProfile: 启用CPU性能分析
 * - bypassLanguageFilter: 绕过语言过滤器
 * - enableCompletionFileEditEvents: 启用补全文件编辑事件
 *
 * 版本控制配置：
 * - vscodeSourcesMinVersion: VSCode源码最小版本
 * - vscodeChatHintDecorationMinVersion: 聊天提示装饰最小版本
 * - vscodeNextEditMinVersion: 下一步编辑最小版本
 * - vscodeGenerateCommitMessageMinVersion: 生成提交消息最小版本
 * - vscodeBackgroundAgentsMinVersion: 后台代理最小版本
 */

// ============================================================================
// 主要激活函数 qun() 详细分析
// ============================================================================

/*
 * qun() 函数是整个插件的入口点，相当于 activate() 函数
 *
 * 函数签名：function qun(r)
 * 参数 r: VSCode扩展上下文 (ExtensionContext)
 *
 * 主要执行流程：
 */

// 1. 初始化阶段
// - 创建日志记录器: let a = Ie("activate()")
// - 输出激活日志: a.debug("======== Activating extension ========")
// - 定义内部函数：
//   * e(e): 启用扩展函数 -> e.enable()
//   * t(e): 禁用扩展函数 -> e.disable()
//   * i(): 重新加载扩展函数 -> t(s), e(s)

// 2. 生命周期管理
// - 注册扩展销毁处理器: r.subscriptions.push(new Bt.Disposable(...))
// - 在扩展销毁时调用 t(s) 禁用扩展并清理资源

// 3. URI处理器注册
// - 注册URI处理器: Bt.window.registerUriHandler({handleUri(e) {...}})
// - 处理的URI路径包括：
//   * /auth/mcp: MCP OAuth回调处理
//   * S.authRedirectURI.path: 认证重定向URI
//   * zte.openChat: 打开聊天界面
//   * zte.openAugmentSettings: 打开Augment设置
//   * zte.openGuidelinesSettings: 打开指导原则设置
//   * zte.openMemories: 打开记忆文件

// 4. 系统信息收集
// - 收集平台信息: Mte.default.platform(), arch(), release()
// - 构建用户代理字符串: 包含扩展ID、版本、平台信息、VSCode版本

// 5. 核心服务初始化
// - 创建全局状态管理器: o = new JV(r)
// - 生成会话ID: l = j8e(o)
// - 创建配置监听器: c = new fH
// - 迁移旧配置: c.migrateLegacyConfig()
// - 创建认证管理器: u = new yH(r, c)
// - 创建同步启用跟踪器: d = new Vee

// 6. API和网络服务初始化
// - 创建API服务器: p = new dH(c, u, l, n, global.fetch)
// - 创建各种缓存和事件发射器:
//   * h = new kZ (完成缓存)
//   * f = new rA(10) (最近完成)
//   * g = new rA(10) (最近指令)
//   * m = new rA(10) (最近下一步编辑结果)
//   * _ = new Bt.EventEmitter (下一步编辑WebView事件)
//   * A = new Bt.EventEmitter (扩展更新事件)
//   * v = new Bt.EventEmitter (WebView应用变更事件)
//   * y = new Bt.EventEmitter (聊天扩展事件)

// 7. 工具和资源管理器初始化
// - 创建资产管理器: b = new fK(r)
// - 创建Git操作服务: w = new FN(new m8({defaultBackupDir: r.storageUri?.fsPath}))
// - 创建主面板提供器: E = new Nee(r.extensionUri)
// - 设置可见性变更处理: E.onVisibilityChange(...)

// 8. 状态和指标管理
// - 创建入职会话事件报告器: C = new BZ(o)
// - 创建客户端指标报告器: k = new JZ(p)
// - 创建工具配置存储: S = new tK(r, c, p, u, k)
// - 创建登录应用: I = new FZ(p, c, S, C)

// 9. 状态处理函数定义
// - O(e): 处理状态变更的函数
//   * "UserShouldSignIn": 用户需要登录
//   * "WorkspaceNotSelected": 工作区未选择
//   * "ShouldDisableCopilot": 应该禁用Copilot
//   * "ShouldDisableCodeium": 应该禁用Codium
//   * "SyncingPermissionNeeded": 需要同步权限
//   * "uploadingHomeDir": 正在上传主目录
//   * "workspaceTooLarge": 工作区太大

// - W(e): 焦点管理函数
//   * 如果未禁用自动焦点且面板不可见，则执行打开命令

// 10. 事件监听器注册
// - 会话变更监听: u.onDidChangeSession(() => i())
// - 状态管理器: new bH(C, u, c)
// - WebView提供器注册: Bt.window.registerWebviewViewProvider("augment-chat", E, ...)

// 11. 主扩展实例创建
// - 创建主扩展实例: s = new U9(r, o, c, p, u, h, f, m, g, _, y, E, v, C, d, A, k, b, w)
// - 这是整个插件的核心管理器，包含所有主要功能模块

// 12. 配置变更监听
// - 监听配置变更: c.onDidChange(e => {...})
// - 当关键配置项变更时重新加载扩展:
//   * apiToken: API令牌
//   * completionURL: 补全服务URL
//   * oauth: OAuth配置
//   * modelName: 模型名称

// 13. 最终注册和启用
// - 注册文本文档内容提供器: Bt.workspace.registerTextDocumentContentProvider(U9.contentScheme, s)
// - 执行额外初始化: Dft(r), fkt(s, c, r)
// - 创建并注册命令处理器: n = kCt(...)
// - 注册各种处理器: AH.register(r, c), mH.register(r, c, s)
// - 最终启用扩展: e(s)

// ============================================================================
// 核心扩展类 U9 分析
// ============================================================================

/*
 * U9 类是整个插件的核心管理器类，包含所有主要功能模块
 *
 * 构造函数参数分析：
 * - r: 扩展上下文 (ExtensionContext)
 * - o: 全局状态管理器 (GlobalState)
 * - c: 配置监听器 (ConfigListener)
 * - p: API服务器 (ApiServer)
 * - u: 认证管理器 (AuthManager)
 * - h: 完成缓存 (CompletionCache)
 * - f: 最近完成 (RecentCompletions)
 * - m: 最近指令 (RecentInstructions)
 * - g: 最近下一步编辑结果 (RecentNextEditResults)
 * - _: 下一步编辑WebView事件 (NextEditWebViewEvent)
 * - y: 聊天扩展事件 (ChatExtensionEvent)
 * - E: 主面板提供器 (MainPanelProvider)
 * - v: WebView应用变更事件 (ChangeWebviewAppEvent)
 * - C: 入职会话事件报告器 (OnboardingSessionEventReporter)
 * - d: 同步启用跟踪器 (SyncingEnabledTracker)
 * - A: 扩展更新事件 (ExtensionUpdateEvent)
 * - k: 客户端指标报告器 (ClientMetricsReporter)
 * - b: 资产管理器 (AssetManager)
 * - w: Git操作服务 (GitOperationsService)
 *
 * 主要功能模块：
 * - 状态栏管理: this._statusBar = new Wee
 * - 工作计时器: this.workTimer = new ZZ
 * - 特性标志管理器: this.featureFlagManager = new iH
 * - 完成接受报告器: this._completionAcceptanceReporter = new UZ
 * - 代码编辑报告器: this._codeEditReporter = new qZ
 * - 下一步编辑解决报告器: this._nextEditResolutionReporter = new jZ
 * - 下一步编辑会话事件报告器: this._nextEditSessionEventReporter = new KZ
 * - 下一步编辑配置管理器: this.nextEditConfigManager = new wK
 * - 客户端指标报告器: this._clientMetricsReporter = new QZ
 * - 完成时间线报告器: this._completionTimelineReporter = new VZ
 * - 扩展事件报告器: this._extensionEventReporter = new GZ
 * - 特征向量报告器: this._featureVectorReporter = new LZ
 * - 指导原则监听器: this.guidelinesWatcher = new Lg
 * - 规则监听器: this.rulesWatcher = new c4
 * - 工具使用请求事件报告器: this._toolUseRequestEventReporter = new $N
 * - 完成模型: this._completionsModel = new vZ
 */

// ============================================================================
// VSCode插件核心类和激活函数
// ============================================================================

/**
 * VSCode Augment插件主类 - U9
 * 这是整个插件的核心控制器，管理所有功能模块
 */
class AugmentExtension extends ze {  // U9 -> AugmentExtension, ze -> BaseClass
    constructor(extensionContext, globalState, configListener, apiServer, auth,
                recentCompletions, recentInstructions, recentNextEditResults,
                recentChats, nextEditWebViewEvent, onExtensionUpdateEvent,
                mainPanelProvider, changeWebviewAppEvent, actionsModel,
                syncingEnabledTracker, chatExtensionEvent, onboardingSessionEventReporter,
                assetManager, gitOperationsService) {

        super();

        // 核心上下文和状态管理
        this._extensionContext = extensionContext;           // VSCode扩展上下文
        this._globalState = globalState;                     // 全局状态存储
        this._augmentConfigListener = configListener;       // 配置监听器
        this._apiServer = apiServer;                         // API服务器
        this._auth = auth;                                   // 认证管理器

        // 最近活动记录
        this._recentCompletions = recentCompletions;         // 最近的代码补全
        this._recentInstructions = recentInstructions;       // 最近的指令
        this._recentNextEditResults = recentNextEditResults; // 最近的下一步编辑结果
        this._recentChats = recentChats;                     // 最近的聊天记录

        // 事件发射器
        this._nextEditWebViewEvent = nextEditWebViewEvent;   // 下一步编辑WebView事件
        this._onExtensionUpdateEvent = onExtensionUpdateEvent; // 扩展更新事件
        this._changeWebviewAppEvent = changeWebviewAppEvent; // WebView应用变更事件
        this._chatExtensionEvent = chatExtensionEvent;       // 聊天扩展事件

        // UI和状态管理
        this._mainPanelProvider = mainPanelProvider;         // 主面板提供器
        this._actionsModel = actionsModel;                   // 操作模型
        this._syncingEnabledTracker = syncingEnabledTracker; // 同步启用跟踪器
        this._onboardingSessionEventReporter = onboardingSessionEventReporter; // 入职会话事件报告器
        this._assetManager = assetManager;                   // 资产管理器
        this._gitOperationsService = gitOperationsService;   // Git操作服务

        // 初始化核心组件
        this._statusBar = new StatusBarManager();           // Wee -> StatusBarManager
        extensionContext.subscriptions.push(this._statusBar);

        this.workTimer = new WorkTimer();                   // ZZ -> WorkTimer

        // 特性标志管理器
        this.featureFlagManager = new FeatureFlagManager({  // iH -> FeatureFlagManager
            fetcher: this._fetchFeatureFlags.bind(this),
            refreshIntervalMSec: 1800000  // 30分钟
        }, this._augmentConfigListener);

        // 各种事件报告器
        this._completionAcceptanceReporter = new CompletionAcceptanceReporter(apiServer, this._onboardingSessionEventReporter); // UZ -> CompletionAcceptanceReporter
        this._codeEditReporter = new CodeEditReporter(apiServer);                    // qZ -> CodeEditReporter
        this._nextEditResolutionReporter = new NextEditResolutionReporter(apiServer); // jZ -> NextEditResolutionReporter
        this._nextEditSessionEventReporter = new NextEditSessionEventReporter(apiServer); // KZ -> NextEditSessionEventReporter

        // 配置管理器
        this.nextEditConfigManager = new NextEditConfigManager(               // wK -> NextEditConfigManager
            this._augmentConfigListener,
            this.featureFlagManager,
            this._globalState
        );

        // 更多报告器
        this._clientMetricsReporter = new ClientMetricsReporter(apiServer);   // QZ -> ClientMetricsReporter
        this._completionTimelineReporter = new CompletionTimelineReporter(apiServer); // VZ -> CompletionTimelineReporter
        this._extensionEventReporter = new ExtensionEventReporter(apiServer); // GZ -> ExtensionEventReporter
        this._featureVectorReporter = new FeatureVectorReporter(apiServer, extensionContext); // LZ -> FeatureVectorReporter

        // 文件监听器
        this.guidelinesWatcher = new GuidelinesWatcher(                       // Lg -> GuidelinesWatcher
            this._augmentConfigListener,
            this.featureFlagManager,
            this._clientMetricsReporter
        );
        this.rulesWatcher = new RulesWatcher(this.workspaceManager);          // c4 -> RulesWatcher

        // 工具使用请求事件报告器
        this._toolUseRequestEventReporter = new ToolUseRequestEventReporter(); // $N -> ToolUseRequestEventReporter

        // 添加到销毁列表
        this.disposeOnDisable.push(this.guidelinesWatcher);
        this.disposeOnDisable.push(this.rulesWatcher);
        this.addDisposable(new Bt.Disposable(() => this.disable()));

        // 完成模型
        this._completionsModel = new CompletionsModel(                        // vZ -> CompletionsModel
            this,
            this._augmentConfigListener,
            this._clientMetricsReporter
        );

        // macOS证书处理
        if (!isVSCodeVersionAtLeast("1.96.0")) {  // qo -> isVSCodeVersionAtLeast
            try {
                this._logger.info("Starting macCA");
                addToGlobalAgent();  // pkt.addToGlobalAgent -> addToGlobalAgent
                this._logger.info("macCa Done");
            } catch (error) {
                this._logger.error("Exception loading mac-ca certs:", error);
            }
        }
    }

    // 静态属性
    static augmentRootName = ".augmentroot";
    static contentScheme = "augment";
    static displayStatusUri = Bt.Uri.from({
        scheme: this.contentScheme,
        path: "Augment Extension Status"
    });
    static modelConfigBackoffMsecMax = 30000; // 30秒

    // 实例属性
    keybindingWatcher = undefined;
    _completionServer = undefined;
    workspaceManager = undefined;

    // 更多核心属性
    _modelInfo = undefined;                          // 模型信息
    _blobNameCalculator = undefined;                 // Blob名称计算器
    _suggestionManager = undefined;                  // 建议管理器
    _nextEditRequestManager = undefined;             // 下一步编辑请求管理器
    _editorNextEdit = undefined;                     // 编辑器下一步编辑
    _backgroundNextEdit = undefined;                 // 后台下一步编辑
    _globalNextEdit = undefined;                     // 全局下一步编辑
    _diagnosticsManager = undefined;                 // 诊断管理器
    _nextEditVSCodeToWebviewMessage = new Bt.EventEmitter(); // VSCode到WebView消息事件发射器
    _openChatHintManager = undefined;                // 打开聊天提示管理器
    _remoteWorkspaceResolver = undefined;            // 远程工作区解析器
    _analyticsManager = undefined;                   // 分析管理器
    _inlineCompletionProvider = undefined;           // 内联补全提供器
    _chatModel = undefined;                          // 聊天模型
    _currentChatExtensionEventDisposable = undefined; // 当前聊天扩展事件可销毁对象
    _notificationWatcher = undefined;                // 通知监听器
    _toolsModel = undefined;                         // 工具模型
    _taskManager = undefined;                        // 任务管理器
    _exchangeManager = undefined;                    // 交换管理器
    _toolUseStateManager = undefined;                // 工具使用状态管理器
    _agentCheckpointManager = undefined;             // 代理检查点管理器
    _toolConfigStore = undefined;                    // 工具配置存储
    toolApprovalConfigManager = undefined;           // 工具批准配置管理器
    syncingStatusReporter = undefined;               // 同步状态报告器
    fuzzyFsSearcher = undefined;                     // 模糊文件系统搜索器
    fuzzySymbolSearcher = undefined;                 // 模糊符号搜索器

    // 状态属性
    enabled = false;                                 // 是否已启用
    enableInProgress = false;                        // 是否正在启用中
    disposeOnDisable = [];                          // 禁用时需要销毁的对象列表
    _enableCancel = undefined;                       // 启用取消令牌
    _statusTrace = undefined;                        // 状态跟踪
    _dataCollector = undefined;                      // 数据收集器
    _logger = createLogger("AugmentExtension");      // Ie -> createLogger

    // 获取会话ID
    get sessionId() {
        return this._apiServer.sessionId;
    }

    // 获取是否准备就绪
    get ready() {
        return this.enabled && this._modelInfo !== undefined;
    }

    // 获取可用模型列表
    get _availableModels() {
        return this._modelInfo?.models?.map(model => model.name) || [];
    }

    // 获取默认模型
    get _defaultModel() {
        return this._modelInfo?.defaultModel;
    }

    // 获取支持的语言列表
    get _languages() {
        return this._modelInfo?.languages || [];
    }
}

// ============================================================================
// VSCode插件激活函数
// ============================================================================

/**
 * VSCode插件激活函数 - qun
 * 这是VSCode插件的主入口点，当插件被激活时调用
 *
 * @param {vscode.ExtensionContext} extensionContext - VSCode扩展上下文
 */
function activateAugmentExtension(extensionContext) {  // qun -> activateAugmentExtension
    let logger = createLogger("activate()");  // Ie -> createLogger
    logger.debug("======== Activating extension ========");

    let augmentExtension; // s -> augmentExtension

    // 启用扩展
    function enableExtension(extension) {  // e -> enableExtension
        extension.enable();
    }

    // 禁用扩展
    function disableExtension(extension) {  // t -> disableExtension
        extension.disable();
    }

    // 重新加载扩展
    function reloadExtension() {  // i -> reloadExtension
        logger.info("======== Reloading extension ========");
        disableExtension(augmentExtension);
        enableExtension(augmentExtension);
    }

    // 注册扩展销毁处理器
    extensionContext.subscriptions.push(new Bt.Disposable(() => {
        if (augmentExtension) {
            logger.debug("======== Deactivating extension ========");
            disableExtension(augmentExtension);
        }
        augmentExtension = undefined;
    }));

    // 注册URI处理器
    extensionContext.subscriptions.push(Bt.window.registerUriHandler({
        handleUri(uri) {
            // 检查URI权限
            if (uri.authority.toLowerCase() !== extensionContext.extension.id.toLowerCase()) {
                logger.warn("Ignoring URI " + uri.toString());
                return;
            }

            // 处理MCP OAuth回调
            if (uri.path.startsWith("/auth/mcp")) {
                logger.info("Detected MCP OAuth callback, routing to handler");
                handleMCPOAuthCallback(uri, augmentExtension?.toolsModel, extensionContext, augmentExtension?.toolConfigStore); // qAt -> handleMCPOAuthCallback
                return;
            }

            // 处理其他URI路径
            switch (uri.path) {
                case AuthRedirectURIHandler.authRedirectURI.path:  // S.authRedirectURI.path
                    AuthRedirectURIHandler.handleAuthURI(uri);     // S.handleAuthURI
                    break;

                case URIActions.openChat:  // zte.openChat
                    var mode = new URLSearchParams(uri.query).get("mode");
                    if (mode && !["agent", "chat"].includes(mode)) {
                        logger.error("Invalid chat mode: " + mode);
                    } else {
                        Bt.commands.executeCommand(OpenChatCommand.commandID, mode); // zC.commandID
                    }
                    break;

                case URIActions.openAugmentSettings:  // zte.openAugmentSettings
                    Bt.commands.executeCommand(OpenSettingsCommand.commandID);     // H9.commandID
                    break;

                case URIActions.openGuidelinesSettings:  // zte.openGuidelinesSettings
                    Bt.commands.executeCommand(OpenSettingsCommand.commandID, "userGuidelines"); // H9.commandID
                    break;

                case URIActions.openMemories:  // zte.openMemories
                    var memoriesPath = augmentExtension?.toolsModel?.memoriesAbsPath;
                    if (memoriesPath) {
                        var memoriesUri = Bt.Uri.file(memoriesPath);
                        Bt.commands.executeCommand("vscode.open", memoriesUri);
                    } else {
                        logger.warn("Could not open memories: path not found.");
                    }
                    break;

                default:
                    logger.error("Unhandled URI " + Bt.Uri.from({
                        scheme: uri.scheme,
                        authority: uri.authority,
                        path: uri.path
                    }).toString());
            }
        }
    }));

    // 构建用户代理字符串
    var platformInfo = `${os.platform()}; ${os.arch()}; ${os.release()}`;  // Mte.default -> os
    var userAgent = `${extensionContext.extension.id}/${extensionContext.extension.packageJSON.version} (${platformInfo}) ${Bt.env.uriScheme}/${Bt.version}`;

    // 创建核心组件
    var globalState = new GlobalStateManager(extensionContext);  // JV -> GlobalStateManager
    extensionContext.subscriptions.push(globalState);

    let sessionId = generateSessionId(globalState);  // j8e -> generateSessionId
    let configListener = new ConfigurationListener(); // fH -> ConfigurationListener
    configListener.migrateLegacyConfig();

    var authManager = new AuthenticationManager(extensionContext, configListener); // yH -> AuthenticationManager
    extensionContext.subscriptions.push(authManager);

    var statusReporter = new StatusReporter(); // Vee -> StatusReporter
    extensionContext.subscriptions.push(statusReporter);

    // 初始化配置管理器
    initializeConfigManager(new ConfigManager(configListener)); // qTe, mK -> initializeConfigManager, ConfigManager
    initializeGlobalConfigManager(new GlobalConfigManager(authManager, configListener)); // $Te, gK -> initializeGlobalConfigManager, GlobalConfigManager

    // 创建API服务器
    let apiServer = new APIServer(configListener, authManager, sessionId, userAgent, global.fetch); // dH -> APIServer
    let statusBar = new StatusBarManager(); // kZ -> StatusBarManager

    // 创建最近活动记录器
    let recentCompletions = new RecentActivityTracker(10);    // rA -> RecentActivityTracker
    let recentInstructions = new RecentActivityTracker(10);
    let recentNextEditResults = new RecentActivityTracker(10);

    // 创建事件发射器
    let nextEditWebViewEvent = new Bt.EventEmitter();
    let onExtensionUpdateEvent = new Bt.EventEmitter();
    let changeWebviewAppEvent = new Bt.EventEmitter();
    let chatExtensionEvent = new Bt.EventEmitter();

    // 创建主面板提供器
    let mainPanelProvider = new MainPanelProvider(extensionContext); // fK -> MainPanelProvider

    // 创建任务管理器
    let taskManager = new TaskManager(new TaskManagerConfig({  // FN, m8 -> TaskManager, TaskManagerConfig
        defaultBackupDir: extensionContext.storageUri?.fsPath
    }));

    // 创建资产管理器
    let assetManager = new AssetManager(extensionContext.extensionUri); // Nee -> AssetManager
    assetManager.onVisibilityChange(isVisible => {
        if (!isVisible) {
            ChatPanel.currentPanel?.dispose(); // mC -> ChatPanel
        }
    });

    // 创建操作模型
    var actionsModel = new ActionsModel(globalState); // BZ -> ActionsModel
    extensionContext.subscriptions.push(actionsModel);

    // 创建同步启用跟踪器
    var syncingEnabledTracker = new SyncingEnabledTracker(apiServer); // JZ -> SyncingEnabledTracker

    // 创建认证状态管理器
    var authStateManager = new AuthenticationStateManager(apiServer, configListener, syncingEnabledTracker); // tK -> AuthenticationStateManager

    // 创建入职会话事件报告器
    var onboardingSessionEventReporter = new OnboardingSessionEventReporter(apiServer, configListener, authStateManager, actionsModel); // FZ -> OnboardingSessionEventReporter

    // 状态处理函数
    function handleSystemStates(states) {  // O -> handleSystemStates
        stateLoop: for (var state of states) {
            switch (state.name) {
                case "UserShouldSignIn":
                    assetManager.changeApp(onboardingSessionEventReporter);
                    showPanelIfNeeded(configListener.config);
                    break stateLoop;
                case "WorkspaceNotSelected":
                    if (actionsModel.isSystemStateComplete("authenticated")) {
                        changeWebviewAppEvent.fire("folder-selection");
                    }
                    break stateLoop;
                case "ShouldDisableCopilot":
                case "ShouldDisableCodeium":
                case "SyncingPermissionNeeded":
                case "uploadingHomeDir":
                case "workspaceTooLarge":
                    showPanelIfNeeded(configListener.config);
                    break stateLoop;
            }
        }

        // 检查是否需要同步权限
        if (actionsModel.isDerivedStateSatisfied("SyncingPermissionNeeded") ||
            actionsModel.isDerivedStateSatisfied("uploadingHomeDir") ||
            actionsModel.isDerivedStateSatisfied("workspaceTooLarge")) {
            changeWebviewAppEvent.fire("awaiting-syncing-permission");
        }
    }

    // 显示面板函数
    function showPanelIfNeeded(config) {  // W -> showPanelIfNeeded
        if (!config.disableFocusOnAugmentPanel && !assetManager.isVisible()) {
            Bt.commands.executeCommand(ShowPanelCommand.commandID); // uo.commandID
        }
    }

    // 注册会话变更监听器
    extensionContext.subscriptions.push(authManager.onDidChangeSession(() => {
        reloadExtension();
    }));

    // 注册状态监听器
    extensionContext.subscriptions.push(new StateListener(actionsModel, authManager, configListener)); // bH -> StateListener

    // 注册WebView提供器
    extensionContext.subscriptions.push(Bt.window.registerWebviewViewProvider("augment-chat", assetManager, {
        webviewOptions: {
            retainContextWhenHidden: true
        }
    }));

    // 创建主扩展实例
    augmentExtension = new AugmentExtension(  // U9 -> AugmentExtension
        extensionContext,
        globalState,
        configListener,
        apiServer,
        authManager,
        statusBar,
        recentCompletions,
        recentNextEditResults,
        recentInstructions,
        nextEditWebViewEvent,
        chatExtensionEvent,
        assetManager,
        changeWebviewAppEvent,
        actionsModel,
        statusReporter,
        onExtensionUpdateEvent,
        syncingEnabledTracker,
        mainPanelProvider,
        taskManager
    );

    // 注册状态监听器
    extensionContext.subscriptions.push(actionsModel.onDerivedStatesSatisfied(handleSystemStates));
    handleSystemStates(actionsModel.satisfiedStates);

    // 监听配置变更
    configListener.onDidChange(configChange => {
        // 检查调试功能是否变更
        configChange.newConfig.enableDebugFeatures;
        configChange.previousConfig.enableDebugFeatures;
    });

    // 监听重要配置变更并重新加载扩展
    extensionContext.subscriptions.push(configListener.onDidChange(configChange => {
        let shouldReload = false;

        // 检查关键配置项是否变更
        for (var configKey of ["apiToken", "completionURL", "oauth", "modelName"]) {
            if (!isEqual(configChange.previousConfig[configKey], configChange.newConfig[configKey])) { // G8e.default -> isEqual
                shouldReload = true;
                break;
            }
        }

        if (shouldReload) {
            logger.info("Reloading extension due to config change");
            reloadExtension();
        }
    }));

    // 注册内容提供器
    extensionContext.subscriptions.push(Bt.workspace.registerTextDocumentContentProvider(AugmentExtension.contentScheme, augmentExtension));

    // 初始化其他功能
    initializeDebugFeatures(extensionContext); // Dft -> initializeDebugFeatures
    setupContextKeySync(augmentExtension, configListener, extensionContext); // fkt -> setupContextKeySync

    // 注册命令和功能
    var commandRegistrations = registerCommands(  // kCt -> registerCommands
        extensionContext,
        augmentExtension,
        configListener,
        authManager,
        authStateManager,
        apiServer,
        statusBar,
        recentCompletions,
        recentNextEditResults,
        changeWebviewAppEvent,
        onExtensionUpdateEvent,
        statusReporter,
        globalState,
        extensionContext.workspaceState
    );
    extensionContext.subscriptions.push(commandRegistrations);

    // 注册事件发射器
    extensionContext.subscriptions.push(chatExtensionEvent);

    // 注册其他功能
    extensionContext.subscriptions.push(AnalyticsHandler.register(extensionContext, configListener)); // AH -> AnalyticsHandler
    extensionContext.subscriptions.push(ModelInfoHandler.register(extensionContext, configListener, augmentExtension)); // mH -> ModelInfoHandler

    // 启用扩展
    enableExtension(augmentExtension);
}

// ============================================================================
// 上下文键同步功能
// ============================================================================

/**
 * 设置VSCode上下文键
 * @param {Object} contextKeys - 上下文键对象
 */
function setContextKeys(contextKeys) {  // ckt -> setContextKeys
    for (var [key, value] of Object.entries(contextKeys)) {
        Bt.commands.executeCommand("setContext", key, value);
    }
}

/**
 * 设置上下文键同步
 * @param {AugmentExtension} extension - 扩展实例
 * @param {ConfigurationListener} configListener - 配置监听器
 * @param {vscode.ExtensionContext} extensionContext - 扩展上下文
 */
function setupContextKeySync(extension, configListener, extensionContext) {  // fkt -> setupContextKeySync
    // 基础上下文键更新函数
    var updateBasicContextKeys = () => {  // a -> updateBasicContextKeys
        var config = configListener.config;
        setContextKeys({
            "vscode-augment.enableDebugFeatures": config.enableDebugFeatures,
            "vscode-augment.enableReviewerWorkflows": config.enableReviewerWorkflows,
            "vscode-augment.enableNextEdit": isNextEditEnabled(configListener.config, extension?.featureFlagManager.currentFlags.vscodeNextEditMinVersion ?? ""), // kf -> isNextEditEnabled
            "vscode-augment.enableNextEditBackgroundSuggestions": isNextEditBackgroundEnabled(configListener.config, extension?.featureFlagManager.currentFlags.vscodeNextEditMinVersion ?? ""), // vO -> isNextEditBackgroundEnabled
            "vscode-augment.enableGenerateCommitMessage": isVersionAtLeast(extension?.featureFlagManager.currentFlags.vscodeGenerateCommitMessageMinVersion ?? ""), // or -> isVersionAtLeast
            "vscode-augment.nextEdit.enablePanel": extension.nextEditConfigManager.config.enablePanel,
            "vscode-augment.featureFlags.enableRemoteAgents": isVersionAtLeast(extension?.featureFlagManager.currentFlags.vscodeBackgroundAgentsMinVersion ?? "") ?? false
        });
    };

    // 初始化基础上下文键
    updateBasicContextKeys();
    extensionContext.subscriptions.push(configListener.onDidChange(updateBasicContextKeys));

    // 特性标志相关的上下文键
    var featureFlagKeys = [  // s -> featureFlagKeys
        "enableWorkspaceManagerUi",
        "enableSmartPaste",
        "enableSmartPasteMinVersion",
        "enableInstructions",
        "vscodeSourcesMinVersion",
        "vscodeChatHintDecorationMinVersion",
        "vscodeEnableCpuProfile",
        "vscodeNextEditMinVersion",
        "vscodeGenerateCommitMessageMinVersion"
    ];

    // 特性标志上下文键更新函数
    var updateFeatureFlagContextKeys = () => {  // i -> updateFeatureFlagContextKeys
        if (extension) {
            var flags = extension.featureFlagManager.currentFlags;
            setContextKeys({
                "vscode-augment.workspace-manager-ui.enabled": flags.enableWorkspaceManagerUi,
                "vscode-augment.internal-new-instructions.enabled": flags.enableInstructions,
                "vscode-augment.internal-dv.enabled": isVersionAtLeast(flags.enableSmartPasteMinVersion) || flags.enableInstructions,
                "vscode-augment.sources-enabled": isVersionAtLeast(flags.vscodeSourcesMinVersion) ?? false,
                "vscode-augment.chat-hint.decoration": isVersionAtLeast(flags.vscodeChatHintDecorationMinVersion) ?? false,
                "vscode-augment.cpu-profile.enabled": flags.vscodeEnableCpuProfile,
                "vscode-augment.enableGenerateCommitMessage": isVersionAtLeast(flags.vscodeGenerateCommitMessageMinVersion) ?? false,
                "vscode-augment.featureFlags.enableRemoteAgents": isVersionAtLeast(flags.vscodeBackgroundAgentsMinVersion) ?? false,
                "vscode-augment.nextEdit.enablePanel": extension.nextEditConfigManager.config.enablePanel
            });
        }
    };

    // 初始化特性标志上下文键
    updateFeatureFlagContextKeys();
    extensionContext.subscriptions.push(extension.featureFlagManager.subscribe(featureFlagKeys, updateFeatureFlagContextKeys));
    extensionContext.subscriptions.push(extension.featureFlagManager.subscribe(featureFlagKeys, updateBasicContextKeys));
}

// 导出上下文键同步功能
var ContextKeySync = {  // Uun -> ContextKeySync
    setupContextKeySync: setupContextKeySync
};

// ============================================================================
// 异步工具函数模块
// ============================================================================

// Promise超时和回调处理工具
var promiseUtils = moduleWrapper((exports, module) => {  // mne -> promiseUtils
    // Promise超时包装器
    function promiseTimeout(promise, timeoutMs) {
        return new Promise(function(resolve, reject) {
            var timeoutId = setTimeout(function() {
                reject(Error("Promise timed out"))
            }, timeoutMs);
            promise.then(function(result) {
                return clearTimeout(timeoutId), resolve(result)
            }).catch(reject)
        })
    }

    // 延迟执行工具
    function sleep(delayMs) {
        return new Promise(function(resolve) {
            return setTimeout(resolve, delayMs)
        })
    }

    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.invokeCallback = exports.sleep = exports.pTimeout = void 0;
    exports.pTimeout = promiseTimeout;
    exports.sleep = sleep;

    // 回调函数调用器 - 带超时和错误处理
    exports.invokeCallback = function(context, callback, delay) {
        return sleep(delay).then(function() {
            return promiseTimeout((() => {
                try {
                    return Promise.resolve(callback(context))
                } catch (error) {
                    return Promise.reject(error)
                }
            })(), 1000)  // 1秒超时
        }).catch(function(error) {
            context?.log("warn", "Callback Error", {
                error: error
            });
            context?.stats.increment("callback_error")
        }).then(function() {
            return context
        })
    }
});

// ============================================================================
// Deferred Promise 工具
// ============================================================================

// 可延迟解决的Promise创建器
var createDeferredPromise = moduleWrapper((exports, module) => {  // CSe -> createDeferredPromise
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.createDeferred = void 0;

    exports.createDeferred = function() {
        var resolveFunc, rejectFunc, isSettled = false,
            promise = new Promise(function(resolve, reject) {
                resolveFunc = function() {
                    for (var args = [], i = 0; i < arguments.length; i++) args[i] = arguments[i];
                    isSettled = true;
                    resolve.apply(void 0, args)
                };
                rejectFunc = function() {
                    for (var args = [], i = 0; i < arguments.length; i++) args[i] = arguments[i];
                    isSettled = true;
                    reject.apply(void 0, args)
                }
            });
        return {
            resolve: resolveFunc,
            reject: rejectFunc,
            promise: promise,
            isSettled: function() {
                return isSettled
            }
        }
    }
});

// Deferred Promise 导出模块
var deferredExports = moduleWrapper((exports, module) => {  // SSe -> deferredExports
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    commonJSHelpers();
    markAsESModule(commonJSUtils).__exportStar(createDeferredPromise(), exports)
});

// ============================================================================
// 事件发射器 (Event Emitter) 模块
// ============================================================================

// 事件发射器实现
var eventEmitter = moduleWrapper((exports, module) => {  // xSe -> eventEmitter
    function EventEmitter(options) {
        this.callbacks = {};
        this.warned = false;
        this.maxListeners = null != (options = options?.maxListeners) ? options : 10
    }

    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.Emitter = void 0;

    // 内存泄漏警告检查
    EventEmitter.prototype.warnIfPossibleMemoryLeak = function(eventName) {
        if (!this.warned && this.maxListeners && this.callbacks[eventName].length > this.maxListeners) {
            console.warn("Event Emitter: Possible memory leak detected; ".concat(String(eventName), " has exceeded ").concat(this.maxListeners, " listeners."));
            this.warned = true
        }
    };

    // 添加事件监听器
    EventEmitter.prototype.on = function(eventName, callback) {
        if (this.callbacks[eventName]) {
            this.callbacks[eventName].push(callback);
            this.warnIfPossibleMemoryLeak(eventName)
        } else {
            this.callbacks[eventName] = [callback]
        }
        return this
    };

    // 添加一次性事件监听器
    EventEmitter.prototype.once = function(eventName, callback) {
        function onceWrapper() {
            for (var args = [], i = 0; i < arguments.length; i++) args[i] = arguments[i];
            emitterInstance.off(eventName, onceWrapper);
            callback.apply(emitterInstance, args)
        }
        var emitterInstance = this;
        return this.on(eventName, onceWrapper), this
    };

    // 移除事件监听器
    EventEmitter.prototype.off = function(eventName, callback) {
        var filteredCallbacks = (null != (filteredCallbacks = this.callbacks[eventName]) ? filteredCallbacks : []).filter(function(cb) {
            return cb !== callback
        });
        return this.callbacks[eventName] = filteredCallbacks, this
    };

    // 触发事件
    EventEmitter.prototype.emit = function(eventName) {
        for (var emitterInstance = this, args = [], i = 1; i < arguments.length; i++) args[i - 1] = arguments[i];
        return (null != (eventName = this.callbacks[eventName]) ? eventName : []).forEach(function(callback) {
            callback.apply(emitterInstance, args)
        }), this
    };

    exports.Emitter = EventEmitter
});

// 事件发射器导出模块
var emitterExports = moduleWrapper((exports, module) => {  // kSe -> emitterExports
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    commonJSHelpers();
    markAsESModule(commonJSUtils).__exportStar(eventEmitter(), exports)
});

// 工具模块聚合导出
var toolsAggregator = moduleWrapper((exports, module) => {  // TA -> toolsAggregator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils);
    commonJSUtils.__exportStar(deferredExports(), exports);
    commonJSUtils.__exportStar(emitterExports(), exports)
});

// ============================================================================
// 退避算法模块 (Backoff Algorithm)
// ============================================================================

// 指数退避算法实现
var backoffAlgorithm = moduleWrapper((exports, module) => {  // bne -> backoffAlgorithm
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.backoff = void 0;

    // 计算退避延迟时间
    exports.backoff = function(options) {
        var randomFactor = Math.random() + 1,
            minTimeout = options.minTimeout,
            factor = options.factor,
            maxTimeout = void 0 === (maxTimeout = options.maxTimeout) ? Infinity : maxTimeout;
        return Math.min(randomFactor * (void 0 === minTimeout ? 500 : minTimeout) * Math.pow(void 0 === factor ? 2 : factor, options.attempt), maxTimeout)
    }
});

// ============================================================================
// 优先级队列模块 (Priority Queue)
// ============================================================================

// 优先级队列实现 - 用于任务调度和重试机制
var priorityQueue = moduleWrapper((exports, module) => {  // vne -> priorityQueue
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.PriorityQueue = exports.ON_REMOVE_FROM_FUTURE = void 0;

    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils),
        toolsModule = toolsAggregator(),
        backoffModule = backoffAlgorithm();

    exports.ON_REMOVE_FROM_FUTURE = "onRemoveFromFuture";

    var EmitterBase = toolsModule.Emitter;

    // 优先级队列类 - 继承自事件发射器
    function PriorityQueue(maxAttempts, initialQueue, seenItems) {
        var instance = EmitterBase.call(this) || this;
        instance.future = [];  // 未来执行的任务队列
        instance.maxAttempts = maxAttempts;  // 最大重试次数
        instance.queue = initialQueue;  // 当前队列
        instance.seen = seenItems ?? {};  // 已见过的项目记录
        return instance
    }

    commonJSUtils.__extends(PriorityQueue, EmitterBase);

    // 向队列添加项目
    PriorityQueue.prototype.push = function() {
        for (var queueInstance = this, items = [], i = 0; i < arguments.length; i++) items[i] = arguments[i];

        var results = items.map(function(item) {
            return !(queueInstance.updateAttempts(item) > queueInstance.maxAttempts ||
                    queueInstance.includes(item) ||
                    (queueInstance.queue.push(item), 0))
        });

        // 按尝试次数排序队列
        this.queue = this.queue.sort(function(a, b) {
            return queueInstance.getAttempts(a) - queueInstance.getAttempts(b)
        });

        return results
    };

    // 带退避延迟的队列添加
    PriorityQueue.prototype.pushWithBackoff = function(item, minDelay) {
        var attempts, queueInstance = this;
        minDelay = void 0 === minDelay ? 0 : minDelay;

        // 如果是首次尝试且没有最小延迟，直接添加
        if (0 == minDelay && 0 === this.getAttempts(item)) {
            return this.push(item)[0]
        }

        attempts = this.updateAttempts(item);

        // 检查是否超过最大尝试次数或已包含在队列中
        if (attempts > this.maxAttempts || this.includes(item)) {
            return false
        }

        // 计算退避延迟
        var backoffDelay = backoffModule.backoff({
            attempt: attempts - 1
        });

        // 应用最小延迟
        if (0 < minDelay && backoffDelay < minDelay) {
            backoffDelay = minDelay
        }

        // 延迟添加到队列
        setTimeout(function() {
            queueInstance.queue.push(item);
            queueInstance.future = queueInstance.future.filter(function(futureItem) {
                return futureItem.id !== item.id
            });
            queueInstance.emit(exports.ON_REMOVE_FROM_FUTURE)
        }, backoffDelay);

        this.future.push(item);
        return true
    };

    // 获取项目的尝试次数
    PriorityQueue.prototype.getAttempts = function(item) {
        return null != (item = this.seen[item.id]) ? item : 0
    };

    // 更新项目的尝试次数
    PriorityQueue.prototype.updateAttempts = function(item) {
        this.seen[item.id] = this.getAttempts(item) + 1;
        return this.getAttempts(item)
    };

    // 检查队列是否包含指定项目
    PriorityQueue.prototype.includes = function(targetItem) {
        return this.queue.includes(targetItem) ||
               this.future.includes(targetItem) ||
               !!this.queue.find(function(item) {
                   return item.id === targetItem.id
               }) ||
               !!this.future.find(function(item) {
                   return item.id === targetItem.id
               })
    };

    // 从队列中取出下一个项目
    PriorityQueue.prototype.pop = function() {
        return this.queue.shift()
    };

    // 队列长度属性
    Object.defineProperty(PriorityQueue.prototype, "length", {
        get: function() {
            return this.queue.length
        },
        enumerable: false,
        configurable: true
    });

    // 总待处理项目数属性
    Object.defineProperty(PriorityQueue.prototype, "todo", {
        get: function() {
            return this.queue.length + this.future.length
        },
        enumerable: false,
        configurable: true
    });

    exports.PriorityQueue = PriorityQueue
});

// ============================================================================
// UUID生成器模块
// ============================================================================

// 简化的UUID v4生成器
var uuidGenerator = moduleWrapper((exports, module) => {  // yne -> uuidGenerator
    var hexChars, poolSize = 256, hexPool = [];

    // 预生成十六进制字符池
    for (; poolSize--;) {
        hexPool[poolSize] = (poolSize + 256).toString(16).substring(1)
    }

    // UUID v4生成函数
    exports.v4 = function() {
        var hexChar, index = 0, uuid = "";

        // 重新生成随机池（如果需要）
        if (!hexChars || 256 < poolSize + 16) {
            hexChars = Array(index = 256);
            for (; index--;) {
                hexChars[index] = 256 * Math.random() | 0
            }
            index = poolSize = 0
        }

        // 构建UUID字符串
        for (; index < 16; index++) {
            hexChar = hexChars[poolSize + index];
            uuid += 6 == index ? hexPool[15 & hexChar | 64] :  // 版本位
                    8 == index ? hexPool[63 & hexChar | 128] :  // 变体位
                    hexPool[hexChar];

            // 添加连字符
            if (1 & index && 1 < index && index < 11) {
                uuid += "-"
            }
        }
        poolSize++;
        return uuid
    }
});

// ============================================================================
// 核心日志记录器模块
// ============================================================================

// 核心日志记录器实现
var coreLogger = moduleWrapper((exports, module) => {  // Ene -> coreLogger
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.CoreLogger = void 0;

    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils);

    function CoreLogger() {
        this._logs = []  // 内部日志存储
    }

    // 记录日志条目
    CoreLogger.prototype.log = function(level, message, extras) {
        var timestamp = new Date;
        this._logs.push({
            level: level,
            message: message,
            time: timestamp,
            extras: extras
        })
    };

    // 日志访问器属性
    Object.defineProperty(CoreLogger.prototype, "logs", {
        get: function() {
            return this._logs
        },
        enumerable: false,
        configurable: true
    });

    // 刷新日志到控制台
    CoreLogger.prototype.flush = function() {
        var formattedLogs;

        if (1 < this.logs.length) {
            // 多条日志时使用表格格式
            formattedLogs = this._logs.reduce(function(accumulator, logEntry) {
                var formattedEntry = commonJSUtils.__assign(commonJSUtils.__assign({}, logEntry), {
                        json: JSON.stringify(logEntry.extras, null, " "),
                        extras: logEntry.extras
                    }),
                    timeKey = (delete formattedEntry.time,
                              null != (timeKey = null == (timeKey = logEntry.time) ? void 0 : timeKey.toISOString()) ? timeKey : "");

                // 处理时间键冲突
                if (accumulator[timeKey]) {
                    timeKey = "".concat(timeKey, "-").concat(Math.random())
                }

                return commonJSUtils.__assign(commonJSUtils.__assign({}, accumulator),
                    ((accumulator = {})[timeKey] = formattedEntry, accumulator))
            }, {});

            // 输出表格或普通日志
            console.table ? console.table(formattedLogs) : console.log(formattedLogs)
        } else {
            // 单条日志直接输出
            this.logs.forEach(function(logEntry) {
                var level = logEntry.level,
                    message = logEntry.message,
                    extras = logEntry.extras;

                if ("info" === level || "debug" === level) {
                    console.log(message, extras ?? "")
                } else {
                    console[level](message, extras ?? "")
                }
            })
        }

        this._logs = []  // 清空日志
    };

    exports.CoreLogger = CoreLogger
});

// ============================================================================
// 统计指标模块
// ============================================================================

// 核心统计指标收集器
var coreStats = moduleWrapper((exports, module) => {  // Cne -> coreStats
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.NullStats = exports.CoreStats = void 0;

    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils);

    // 核心统计类
    function CoreStats() {
        this.metrics = []  // 指标存储
    }

    // 增量计数器
    CoreStats.prototype.increment = function(metricName, value, tags) {
        this.metrics.push({
            metric: metricName,
            value: value = void 0 === value ? 1 : value,
            tags: tags ?? [],
            type: "counter",
            timestamp: Date.now()
        })
    };

    // 仪表盘指标
    CoreStats.prototype.gauge = function(metricName, value, tags) {
        this.metrics.push({
            metric: metricName,
            value: value,
            tags: tags ?? [],
            type: "gauge",
            timestamp: Date.now()
        })
    };

    // 刷新指标到控制台
    CoreStats.prototype.flush = function() {
        var formattedMetrics = this.metrics.map(function(metric) {
            return commonJSUtils.__assign(commonJSUtils.__assign({}, metric), {
                tags: metric.tags.join(",")
            })
        });
        console.table ? console.table(formattedMetrics) : console.log(formattedMetrics);
        this.metrics = []
    };

    // 序列化指标数据
    CoreStats.prototype.serialize = function() {
        return this.metrics.map(function(metric) {
            return {
                m: metric.metric,      // metric name
                v: metric.value,       // value
                t: metric.tags,        // tags
                k: {                   // kind/type
                    gauge: "g",
                    counter: "c"
                }[metric.type],
                e: metric.timestamp    // epoch timestamp
            }
        })
    };

    exports.CoreStats = CoreStats;

    // 空统计类 - 用于禁用统计收集的场景
    var StatsBase = CoreStats;
    function NullStats() {
        return null !== StatsBase && StatsBase.apply(this, arguments) || this
    }

    commonJSUtils.__extends(NullStats, StatsBase);

    // 空实现 - 所有方法都不执行任何操作
    NullStats.prototype.gauge = function() {
        for (var i = 0; i < arguments.length; i++) i, 0
    };

    NullStats.prototype.increment = function() {
        for (var i = 0; i < arguments.length; i++) i, 0
    };

    NullStats.prototype.flush = function() {
        for (var i = 0; i < arguments.length; i++) i, 0
    };

    NullStats.prototype.serialize = function() {
        for (var i = 0; i < arguments.length; i++) i, 0;
        return []
    };

    exports.NullStats = NullStats
});

// ============================================================================
// 核心上下文模块 (Core Context)
// ============================================================================

// 上下文取消和核心上下文实现
var coreContext = moduleWrapper((exports, module) => {  // Sz -> coreContext
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.CoreContext = exports.ContextCancelation = void 0;

    var uuidModule = uuidGenerator(),
        dsetModule = dne(),  // 对象属性设置工具
        loggerModule = coreLogger(),
        statsModule = coreStats();

    // 上下文取消异常类
    function ContextCancelation(options) {
        var defaultValue;
        this.retry = null == (defaultValue = options.retry) || defaultValue;
        this.type = null != (defaultValue = options.type) ? defaultValue : "plugin Error";
        this.reason = null != (defaultValue = options.reason) ? defaultValue : ""
    }

    exports.ContextCancelation = ContextCancelation;

    // 核心上下文类 - 用于跟踪事件处理过程
    function CoreContext(event, contextId, stats, logger) {
        void 0 === contextId && (contextId = uuidModule.v4());
        void 0 === stats && (stats = new statsModule.NullStats);
        void 0 === logger && (logger = new loggerModule.CoreLogger);

        this.attempts = 0;          // 尝试次数
        this.event = event;         // 关联的事件
        this._id = contextId;       // 上下文ID
        this.logger = logger;       // 日志记录器
        this.stats = stats;         // 统计收集器
    }

    // 系统级静态方法
    CoreContext.system = function() {};

    // 检查是否为同一个上下文
    CoreContext.prototype.isSame = function(otherContext) {
        return otherContext.id === this.id
    };

    // 取消上下文执行
    CoreContext.prototype.cancel = function(error) {
        throw error || new ContextCancelation({
            reason: "Context Cancel"
        })
    };

    // 记录日志
    CoreContext.prototype.log = function(level, message, extras) {
        this.logger.log(level, message, extras)
    };

    // 上下文ID访问器
    Object.defineProperty(CoreContext.prototype, "id", {
        get: function() {
            return this._id
        },
        enumerable: false,
        configurable: true
    });

    // 更新事件属性
    CoreContext.prototype.updateEvent = function(propertyPath, value) {
        var integrations;

        // 特殊处理集成配置
        if ("integrations" === propertyPath.split(".")[0]) {
            var integrationName = propertyPath.split(".")[1];
            if (false === (null == (integrations = this.event.integrations) ? void 0 : integrations[integrationName])) {
                return this.event
            }
        }

        // 使用dset设置深层属性
        dsetModule.dset(this.event, propertyPath, value);
        return this.event
    };

    // 获取失败交付信息
    CoreContext.prototype.failedDelivery = function() {
        return this._failedDelivery
    };

    // 设置失败交付信息
    CoreContext.prototype.setFailedDelivery = function(failureInfo) {
        this._failedDelivery = failureInfo
    };

    // 获取日志记录
    CoreContext.prototype.logs = function() {
        return this.logger.logs
    };

    // 刷新日志和统计
    CoreContext.prototype.flush = function() {
        this.logger.flush();
        this.stats.flush()
    };

    // 序列化为JSON
    CoreContext.prototype.toJSON = function() {
        return {
            id: this._id,
            event: this.event,
            logs: this.logger.logs,
            metrics: this.stats.metrics
        }
    };

    exports.CoreContext = CoreContext
});

// ============================================================================
// 数组分组工具模块
// ============================================================================

// 数组分组工具
var arrayGroupBy = moduleWrapper((exports, module) => {  // OSe -> arrayGroupBy
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.groupBy = void 0;

    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils);

    // 按指定键或函数对数组元素进行分组
    exports.groupBy = function(array, groupingKey) {
        var groups = {};

        array.forEach(function(item) {
            var keyValue, groupKey = void 0;

            if ("string" == typeof groupingKey) {
                // 按属性名分组
                keyValue = item[groupingKey];
                groupKey = "string" != typeof keyValue ? JSON.stringify(keyValue) : keyValue
            } else if (groupingKey instanceof Function) {
                // 按函数返回值分组
                groupKey = groupingKey(item)
            }

            if (void 0 !== groupKey) {
                groups[groupKey] = commonJSUtils.__spreadArray(
                    commonJSUtils.__spreadArray([], null != (keyValue = groups[groupKey]) ? keyValue : [], true),
                    [item],
                    false
                )
            }
        });

        return groups
    }
});

// ============================================================================
// Promise检测工具模块
// ============================================================================

// 检测对象是否为thenable (Promise-like)
var thenableDetector = moduleWrapper((exports, module) => {  // WSe -> thenableDetector
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.isThenable = void 0;

    exports.isThenable = function(obj) {
        return "object" == typeof obj && null !== obj && "then" in obj && "function" == typeof obj.then
    }
});

// ============================================================================
// 任务组管理模块
// ============================================================================

// 任务组创建器 - 用于管理异步任务的完成状态
var taskGroupCreator = moduleWrapper((exports, module) => {  // BSe -> taskGroupCreator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.createTaskGroup = void 0;

    var thenableModule = thenableDetector();

    exports.createTaskGroup = function() {
        var donePromise, resolveFunc, activeTaskCount = 0;

        return {
            // 获取所有任务完成的Promise
            done: function() {
                return donePromise
            },

            // 运行任务并跟踪其完成状态
            run: function(taskFunc) {
                var result = taskFunc();

                // 如果返回Promise，跟踪其完成状态
                if (thenableModule.isThenable(result)) {
                    if (1 == ++activeTaskCount) {
                        // 第一个任务时创建完成Promise
                        donePromise = new Promise(function(resolve) {
                            return resolveFunc = resolve
                        })
                    }

                    // 任务完成时减少计数
                    result.finally(function() {
                        return 0 == --activeTaskCount && resolveFunc()
                    })
                }

                return result
            }
        }
    }
});

// ============================================================================
// 插件执行工具模块
// ============================================================================

// 插件尝试执行和确保执行工具
var pluginExecutor = moduleWrapper((exports, module) => {  // xne -> pluginExecutor
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.ensure = exports.attempt = void 0;

    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils),
        contextModule = coreContext();

    // 尝试执行插件方法
    function attemptPluginExecution(context, plugin) {
        context.log("debug", "plugin", {
            plugin: plugin.name
        });

        var startTime = (new Date).getTime(),
            pluginMethod = plugin[context.event.type];

        // 如果插件没有对应事件类型的方法，直接返回
        if (void 0 === pluginMethod) {
            return Promise.resolve(context)
        }

        // 包装插件执行以处理异常
        function executeWithErrorHandling(executionFunc) {
            return commonJSUtils.__awaiter(this, void 0, void 0, function() {
                var error;
                return commonJSUtils.__generator(this, function(step) {
                    switch (step.label) {
                        case 0:
                            step.trys.push([0, 2, , 3]);
                            return [4, executionFunc()];
                        case 1:
                            return [2, step.sent()];
                        case 2:
                            error = step.sent();
                            return [2, Promise.reject(error)];
                        case 3:
                            return [2]
                    }
                })
            })
        }

        // 执行插件方法
        return executeWithErrorHandling(function() {
            return pluginMethod.apply(plugin, [context])
        }).then(function(result) {
            var executionTime = (new Date).getTime() - startTime;
            result.stats.gauge("plugin_time", executionTime, ["plugin:".concat(plugin.name)]);
            return result
        }).catch(function(error) {
            // 处理中间件取消异常
            if (error instanceof contextModule.ContextCancelation && "middleware_cancellation" === error.type) {
                throw error
            }

            // 记录错误
            if (error instanceof contextModule.ContextCancelation) {
                context.log("warn", error.type, {
                    plugin: plugin.name,
                    error: error
                })
            } else {
                context.log("error", "plugin Error", {
                    plugin: plugin.name,
                    error: error
                });
                context.stats.increment("plugin_error", 1, ["plugin:".concat(plugin.name)])
            }

            return error
        })
    }

    exports.attempt = attemptPluginExecution;

    // 确保插件执行成功，否则取消上下文
    exports.ensure = function(context, plugin) {
        return attemptPluginExecution(context, plugin).then(function(result) {
            if (result instanceof contextModule.CoreContext) {
                return result
            }

            // 如果不是有效的上下文，取消执行
            context.log("debug", "Context canceled");
            context.stats.increment("context_canceled");
            context.cancel(result)
        })
    }
});

// ============================================================================
// 核心事件队列模块 (Core Event Queue)
// ============================================================================

// 核心事件队列 - 负责管理和处理事件的分发、重试和插件执行
var coreEventQueue = moduleWrapper((exports, module) => {  // FSe -> coreEventQueue
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.CoreEventQueue = void 0;

    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils),
        groupByModule = arrayGroupBy(),
        priorityQueueModule = priorityQueue(),
        contextModule = coreContext(),
        toolsModule = toolsAggregator(),
        taskGroupModule = taskGroupCreator(),
        pluginExecutorModule = pluginExecutor();

    var EmitterBase = toolsModule.Emitter;

    // 核心事件队列类 - 继承自事件发射器
    function CoreEventQueue(queue) {
        var instance = EmitterBase.call(this) || this;
        instance.criticalTasks = taskGroupModule.createTaskGroup();  // 关键任务组
        instance.plugins = [];                    // 插件列表
        instance.failedInitializations = [];     // 失败的初始化记录
        instance.flushing = false;               // 是否正在刷新
        instance.queue = queue;                  // 事件队列

        // 监听队列的未来任务移除事件
        instance.queue.on(priorityQueueModule.ON_REMOVE_FROM_FUTURE, function() {
            instance.scheduleFlush(0)
        });

        return instance
    }

    commonJSUtils.__extends(CoreEventQueue, EmitterBase);

    // 注册插件
    CoreEventQueue.prototype.register = function(context, plugin, options) {
        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var handleError, error, queueInstance = this;

            return commonJSUtils.__generator(this, function(step) {
                switch (step.label) {
                    case 0:
                        this.plugins.push(plugin);

                        // 错误处理函数
                        handleError = function(err) {
                            queueInstance.failedInitializations.push(plugin.name);
                            queueInstance.emit("initialization_failure", plugin);
                            console.warn(plugin.name, err);
                            context.log("warn", "Failed to load destination", {
                                plugin: plugin.name,
                                error: err
                            });
                            queueInstance.plugins = queueInstance.plugins.filter(function(p) {
                                return p !== plugin
                            })
                        };

                        // 对于目标插件（非Segment.io），异步加载
                        if ("destination" === plugin.type && "Segment.io" !== plugin.name) {
                            plugin.load(context, options).catch(handleError);
                            return [3, 4]
                        }
                        return [3, 1];

                    case 1:
                        step.trys.push([1, 3, , 4]);
                        return [4, plugin.load(context, options)];

                    case 2:
                        step.sent();
                        return [3, 4];

                    case 3:
                        error = step.sent();
                        handleError(error);
                        return [3, 4];

                    case 4:
                        return [2]
                }
            })
        })
    };

    // 注销插件
    CoreEventQueue.prototype.deregister = function(context, plugin, options) {
        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var error;

            return commonJSUtils.__generator(this, function(step) {
                switch (step.label) {
                    case 0:
                        step.trys.push([0, 3, , 4]);

                        if (!plugin.unload) return [3, 2];
                        return [4, Promise.resolve(plugin.unload(context, options))];

                    case 1:
                        step.sent();

                    case 2:
                        this.plugins = this.plugins.filter(function(p) {
                            return p.name !== plugin.name
                        });
                        return [3, 4];

                    case 3:
                        error = step.sent();
                        context.log("warn", "Failed to unload destination", {
                            plugin: plugin.name,
                            error: error
                        });
                        return [3, 4];

                    case 4:
                        return [2]
                }
            })
        })
    };

    // 分发事件到队列
    CoreEventQueue.prototype.dispatch = function(context) {
        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var deliveryPromise;

            return commonJSUtils.__generator(this, function(step) {
                context.log("debug", "Dispatching");
                context.stats.increment("message_dispatched");
                this.queue.push(context);
                deliveryPromise = this.subscribeToDelivery(context);
                this.scheduleFlush(0);
                return [2, deliveryPromise]
            })
        })
    };

    // 订阅交付完成事件
    CoreEventQueue.prototype.subscribeToDelivery = function(targetContext) {
        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var queueInstance = this;

            return commonJSUtils.__generator(this, function(step) {
                return [2, new Promise(function(resolve) {
                    function onFlush(context, success) {
                        if (context.isSame(targetContext)) {
                            queueInstance.off("flush", onFlush);
                            resolve(context)
                        }
                    }
                    queueInstance.on("flush", onFlush)
                })]
            })
        })
    };

    // 单独分发事件（不使用队列）
    CoreEventQueue.prototype.dispatchSingle = function(context) {
        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var queueInstance = this;

            return commonJSUtils.__generator(this, function(step) {
                context.log("debug", "Dispatching");
                context.stats.increment("message_dispatched");
                this.queue.updateAttempts(context);
                context.attempts = 1;

                return [2, this.deliver(context).catch(function(error) {
                    if (queueInstance.enqueuRetry(error, context)) {
                        return queueInstance.subscribeToDelivery(context)
                    } else {
                        context.setFailedDelivery({
                            reason: error
                        });
                        return context
                    }
                })]
            })
        })
    };

    // 检查队列是否为空
    CoreEventQueue.prototype.isEmpty = function() {
        return 0 === this.queue.length
    };

    // 调度刷新操作
    CoreEventQueue.prototype.scheduleFlush = function(delay) {
        var queueInstance = this;
        void 0 === delay && (delay = 500);

        if (!this.flushing) {
            this.flushing = true;
            setTimeout(function() {
                queueInstance.flush().then(function() {
                    setTimeout(function() {
                        queueInstance.flushing = false;
                        if (queueInstance.queue.length) {
                            queueInstance.scheduleFlush(0)
                        }
                    }, 0)
                })
            }, delay)
        }
    };

    // 交付单个事件
    CoreEventQueue.prototype.deliver = function(context) {
        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var startTime, deliveryTime, error, errorDetails;

            return commonJSUtils.__generator(this, function(step) {
                switch (step.label) {
                    case 0:
                        return [4, this.criticalTasks.done()];

                    case 1:
                        step.sent();
                        startTime = Date.now();

                    case 2:
                        step.trys.push([2, 4, , 5]);
                        return [4, this.flushOne(context)];

                    case 3:
                        context = step.sent();
                        deliveryTime = Date.now() - startTime;
                        this.emit("delivery_success", context);
                        context.stats.gauge("delivered", deliveryTime);
                        context.log("debug", "Delivered", context.event);
                        return [2, context];

                    case 4:
                        deliveryTime = step.sent();
                        errorDetails = deliveryTime;
                        context.log("error", "Failed to deliver", errorDetails);
                        this.emit("delivery_failure", context, errorDetails);
                        context.stats.increment("delivery_failed");
                        throw deliveryTime;

                    case 5:
                        return [2]
                }
            })
        })
    };

    // 将失败的事件加入重试队列
    CoreEventQueue.prototype.enqueuRetry = function(error, context) {
        // 检查是否应该重试
        var shouldRetry = !(error instanceof contextModule.ContextCancelation) || error.retry;
        return shouldRetry && this.queue.pushWithBackoff(context)
    };

    // 刷新队列中的下一个事件
    CoreEventQueue.prototype.flush = function() {
        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var context, error;

            return commonJSUtils.__generator(this, function(step) {
                switch (step.label) {
                    case 0:
                        if (0 === this.queue.length) return [2, []];

                        context = this.queue.pop();
                        if (!context) return [2, []];

                        context.attempts = this.queue.getAttempts(context);

                    case 1:
                        step.trys.push([1, 3, , 4]);
                        return [4, this.deliver(context)];

                    case 2:
                        context = step.sent();
                        this.emit("flush", context, true);
                        return [3, 4];

                    case 3:
                        error = step.sent();
                        if (!this.enqueuRetry(error, context)) {
                            context.setFailedDelivery({
                                reason: error
                            });
                            this.emit("flush", context, false)
                        }
                        return [2, []];

                    case 4:
                        return [2, [context]]
                }
            })
        })
    };

    // 检查队列是否准备就绪
    CoreEventQueue.prototype.isReady = function() {
        return true
    };

    // 获取可用的扩展插件（按类型分组）
    CoreEventQueue.prototype.availableExtensions = function(integrations) {
        var availablePlugins = this.plugins.filter(function(plugin) {
                var alternativeValue, alternativeNames;

                // 对于非目标插件或Segment.io，直接返回true
                if ("destination" !== plugin.type && "Segment.io" !== plugin.name) {
                    return true
                }

                // 检查插件的替代名称
                alternativeValue = void 0;
                if (null != (alternativeNames = plugin.alternativeNames)) {
                    alternativeNames.forEach(function(altName) {
                        if (void 0 !== integrations[altName]) {
                            alternativeValue = integrations[altName]
                        }
                    })
                }

                // 确定插件是否启用
                var isEnabled = null != (alternativeNames = null != (alternativeNames = integrations[plugin.name]) ? alternativeNames : alternativeValue) ? alternativeNames : false !== ("Segment.io" === plugin.name || integrations.All);
                return isEnabled
            }),
            groupedPlugins = groupByModule.groupBy(availablePlugins, "type"),
            beforePlugins = groupedPlugins.before,
            enrichmentPlugins = groupedPlugins.enrichment,
            destinationPlugins = groupedPlugins.destination,
            afterPlugins = groupedPlugins.after;

        return {
            before: void 0 === beforePlugins ? [] : beforePlugins,
            enrichment: void 0 === enrichmentPlugins ? [] : enrichmentPlugins,
            destinations: void 0 === destinationPlugins ? [] : destinationPlugins,
            after: void 0 === afterPlugins ? [] : afterPlugins
        }
    };

    // 处理单个事件的完整流程
    CoreEventQueue.prototype.flushOne = function(context) {
        var integrations;

        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var extensions, beforePlugins, enrichmentPlugins, i, plugin, result,
                updatedExtensions, destinationPlugins, afterPlugins, destinationPromises, afterPromises;

            return commonJSUtils.__generator(this, function(step) {
                switch (step.label) {
                    case 0:
                        if (!this.isReady()) {
                            throw new Error("Not ready")
                        }

                        // 如果是重试，发出重试事件
                        if (1 < context.attempts) {
                            this.emit("delivery_retry", context)
                        }

                        // 获取可用扩展
                        extensions = this.availableExtensions(null != (integrations = context.event.integrations) ? integrations : {});
                        beforePlugins = extensions.before;
                        enrichmentPlugins = extensions.enrichment;

                        // 执行before插件
                        i = 0;

                    case 1:
                        if (!(i < beforePlugins.length)) return [3, 4];

                        plugin = beforePlugins[i];
                        return [4, pluginExecutorModule.ensure(context, plugin)];

                    case 2:
                        result = step.sent();
                        if (result instanceof contextModule.CoreContext) {
                            context = result
                        }
                        this.emit("message_enriched", context, plugin);

                    case 3:
                        i++;
                        return [3, 1];

                    case 4:
                        // 执行enrichment插件
                        i = 0;

                    case 5:
                        if (!(i < enrichmentPlugins.length)) return [3, 8];

                        plugin = enrichmentPlugins[i];
                        return [4, pluginExecutorModule.attempt(context, plugin)];

                    case 6:
                        result = step.sent();
                        if (result instanceof contextModule.CoreContext) {
                            context = result
                        }
                        this.emit("message_enriched", context, plugin);

                    case 7:
                        i++;
                        return [3, 5];

                    case 8:
                        // 重新获取扩展（因为context可能已更新）
                        updatedExtensions = this.availableExtensions(null != (integrations = context.event.integrations) ? integrations : {});
                        destinationPlugins = updatedExtensions.destinations;
                        afterPlugins = updatedExtensions.after;

                        // 异步执行所有目标插件
                        return [4, new Promise(function(resolve, reject) {
                            setTimeout(function() {
                                var destinationPromises = destinationPlugins.map(function(plugin) {
                                    return pluginExecutorModule.attempt(context, plugin)
                                });
                                Promise.all(destinationPromises).then(resolve).catch(reject)
                            }, 0)
                        })];

                    case 9:
                        step.sent();
                        context.stats.increment("message_delivered");
                        this.emit("message_delivered", context);

                        // 执行after插件
                        afterPromises = afterPlugins.map(function(plugin) {
                            return pluginExecutorModule.attempt(context, plugin)
                        });
                        return [4, Promise.all(afterPromises)];

                    case 10:
                        step.sent();
                        return [2, context]
                }
            })
        })
    };

    exports.CoreEventQueue = CoreEventQueue
});

// ============================================================================
// 空模块占位符
// ============================================================================

// 空模块 - 用于占位
var emptyModule = moduleWrapper((exports, module) => {  // YSe -> emptyModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    })
});

// ============================================================================
// 事件分发工具模块
// ============================================================================

// 事件分发工具 - 处理事件的分发和回调
var eventDispatcher = moduleWrapper((exports, module) => {  // $Se -> eventDispatcher
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.dispatch = exports.getDelay = void 0;

    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils),
        callbackModule = mne();  // 回调处理模块

    // 计算延迟时间
    exports.getDelay = function(startTime, timeout) {
        var elapsed = Date.now() - startTime;
        return Math.max((timeout ?? 300) - elapsed, 0)
    };

    // 分发事件到队列
    exports.dispatch = function(context, queue, emitter, options) {
        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var startTime, result;

            return commonJSUtils.__generator(this, function(step) {
                switch (step.label) {
                    case 0:
                        emitter.emit("dispatch_start", context);
                        startTime = Date.now();

                        // 根据队列状态选择分发方式
                        if (queue.isEmpty()) {
                            return [4, queue.dispatchSingle(context)]
                        } else {
                            return [3, 2]
                        }

                    case 1:
                        result = step.sent();
                        return [3, 4];

                    case 2:
                        return [4, queue.dispatch(context)];

                    case 3:
                        result = step.sent();

                    case 4:
                        // 处理回调
                        if (null == options ? void 0 : options.callback) {
                            return [4, callbackModule.invokeCallback(result, options.callback, exports.getDelay(startTime, options.timeout))]
                        } else {
                            return [3, 6]
                        }

                    case 5:
                        result = step.sent();

                    case 6:
                        // 调试模式下刷新日志
                        if (null == options ? void 0 : options.debug) {
                            result.flush()
                        }

                        return [2, result]
                }
            })
        })
    }
});

// ============================================================================
// 对象方法绑定工具
// ============================================================================

// 绑定对象所有方法到实例
var methodBinder = moduleWrapper((exports, module) => {  // QSe -> methodBinder
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.bindAll = void 0;

    exports.bindAll = function(instance) {
        var prototype = instance.constructor.prototype,
            propertyNames = Object.getOwnPropertyNames(prototype);

        for (var i = 0; i < propertyNames.length; i++) {
            var propertyName = propertyNames[i],
                descriptor;

            if ("constructor" !== propertyName) {
                descriptor = Object.getOwnPropertyDescriptor(instance.constructor.prototype, propertyName);

                if (descriptor && "function" == typeof descriptor.value) {
                    instance[propertyName] = instance[propertyName].bind(instance)
                }
            }
        }

        return instance
    }
});

// ============================================================================
// 核心工具聚合模块
// ============================================================================

// 核心工具聚合导出 - 汇总所有核心工具
var coreUtilsAggregator = moduleWrapper((exports, module) => {  // zm -> coreUtilsAggregator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.CoreLogger = exports.backoff = void 0;

    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils);

    // 导出各种工具模块
    commonJSUtils.__exportStar(oSe(), exports);      // 对象工具
    commonJSUtils.__exportStar(lSe(), exports);      // 字符串工具
    commonJSUtils.__exportStar(une(), exports);      // 类型检查工具
    commonJSUtils.__exportStar(ySe(), exports);      // 数组工具
    commonJSUtils.__exportStar(mne(), exports);      // 回调工具
    commonJSUtils.__exportStar(priorityQueue(), exports);  // 优先级队列

    // 导出退避算法
    var backoffModule = backoffAlgorithm();
    Object.defineProperty(exports, "backoff", {
        enumerable: true,
        get: function() {
            return backoffModule.backoff
        }
    });

    // 导出其他核心模块
    commonJSUtils.__exportStar(coreContext(), exports);      // 核心上下文
    commonJSUtils.__exportStar(coreEventQueue(), exports);   // 核心事件队列
    commonJSUtils.__exportStar(emptyModule(), exports);      // 空模块
    commonJSUtils.__exportStar(eventDispatcher(), exports);  // 事件分发器
    commonJSUtils.__exportStar(fne(), exports);              // 其他工具
    commonJSUtils.__exportStar(pne(), exports);              // 其他工具
    commonJSUtils.__exportStar(gne(), exports);              // 其他工具
    commonJSUtils.__exportStar(methodBinder(), exports);     // 方法绑定器
    commonJSUtils.__exportStar(coreStats(), exports);        // 核心统计

    // 导出核心日志记录器
    var loggerModule = coreLogger();
    Object.defineProperty(exports, "CoreLogger", {
        enumerable: true,
        get: function() {
            return loggerModule.CoreLogger
        }
    });

    commonJSUtils.__exportStar(pluginExecutor(), exports)    // 插件执行器
});

// ============================================================================
// 设置验证模块
// ============================================================================

// 设置验证器 - 验证配置参数的有效性
var settingsValidator = moduleWrapper((exports, module) => {  // qSe -> settingsValidator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.validateSettings = void 0;

    var coreUtils = coreUtilsAggregator();

    exports.validateSettings = function(settings) {
        if (!settings.writeKey) {
            throw new coreUtils.ValidationError("writeKey", "writeKey is missing.")
        }
    }
});

// ============================================================================
// 版本信息模块
// ============================================================================

// 版本信息
var versionInfo = moduleWrapper((exports, module) => {  // Ine -> versionInfo
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.version = void 0;

    exports.version = "2.2.1"
});

// ============================================================================
// URL格式化工具
// ============================================================================

// URL格式化工具 - 创建格式化的URL
var urlFormatter = moduleWrapper((exports, module) => {  // USe -> urlFormatter
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.tryCreateFormattedUrl = void 0;

    exports.tryCreateFormattedUrl = function(baseUrl, path) {
        return new URL(path || "", baseUrl).href.replace(/\/$/, "")
    }
});

// ============================================================================
// UUID工具导出模块
// ============================================================================

// UUID工具导出 - 重新导出UUID生成功能
var uuidExporter = moduleWrapper((exports, module) => {  // zz -> uuidExporter
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.uuid = void 0;

    var uuidModule = uuidGenerator();

    Object.defineProperty(exports, "uuid", {
        enumerable: true,
        get: function() {
            return uuidModule.v4
        }
    })
});

// ============================================================================
// 上下文批处理模块
// ============================================================================

// 上下文批处理类 - 用于批量处理事件
var contextBatch = moduleWrapper((exports, module) => {  // GSe -> contextBatch
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.ContextBatch = void 0;

    var uuidExports = uuidExporter();

    // 上下文批处理类
    var ContextBatch = /** @class */ (function() {
        function ContextBatch(maxEventCount) {
            this.id = uuidExports.uuid();           // 批次ID
            this.items = [];                        // 批次项目
            this.sizeInBytes = 0;                   // 批次大小（字节）
            this.maxEventCount = Math.max(1, maxEventCount)  // 最大事件数量
        }

        // 尝试添加事件到批次
        ContextBatch.prototype.tryAdd = function(eventItem) {
            var eventSize;

            // 检查事件数量限制
            if (this.length === this.maxEventCount) {
                return {
                    success: false,
                    message: "Event limit of ".concat(this.maxEventCount, " has been exceeded.")
                }
            }

            // 计算事件大小
            eventSize = this.calculateSize(eventItem.context);

            // 检查单个事件大小限制（32KB）
            if (32768 < eventSize) {
                return {
                    success: false,
                    message: "Event exceeds maximum event size of 32 KB"
                }
            }

            // 检查批次总大小限制（480KB）
            if (491520 < this.sizeInBytes + eventSize) {
                return {
                    success: false,
                    message: "Event has caused batch size to exceed 480 KB"
                }
            }

            // 添加事件到批次
            this.items.push(eventItem);
            this.sizeInBytes += eventSize;

            return {
                success: true
            }
        };

        // 获取批次长度
        Object.defineProperty(ContextBatch.prototype, "length", {
            get: function() {
                return this.items.length
            },
            enumerable: false,
            configurable: true
        });

        // 计算事件大小
        ContextBatch.prototype.calculateSize = function(context) {
            return encodeURI(JSON.stringify(context.event)).split(/%..|i/).length
        };

        // 获取所有事件
        ContextBatch.prototype.getEvents = function() {
            return this.items.map(function(item) {
                return item.context.event
            })
        };

        // 获取所有上下文
        ContextBatch.prototype.getContexts = function() {
            return this.items.map(function(item) {
                return item.context
            })
        };

        // 解析所有事件（调用resolver）
        ContextBatch.prototype.resolveEvents = function() {
            this.items.forEach(function(item) {
                item.resolver(item.context)
            })
        };

        return ContextBatch
    }());

    exports.ContextBatch = ContextBatch
});

// ============================================================================
// 加密哈希模块
// ============================================================================

// 默认哈希函数 - 使用Node.js crypto模块
var defaultHasher = moduleWrapper((exports, module) => {  // Dne -> defaultHasher
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var crypto = require("crypto");

    exports.default = function(algorithm, data) {
        return crypto.createHash(algorithm).update(data).digest()
    }
});

// ============================================================================
// 字节操作工具模块
// ============================================================================

// 字节操作工具 - 用于编码、解码和字节数组操作
var byteUtils = moduleWrapper((exports, module) => {  // Sa -> byteUtils
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.decoder = exports.encoder = void 0;
    exports.concat = concatArrays;
    exports.p2s = passwordToSalt;
    exports.uint64be = uint64BigEndian;
    exports.uint32be = uint32BigEndian;
    exports.lengthAndInput = lengthAndInput;
    exports.concatKdf = concatKdf;

    var hasherModule = defaultHasher(),
        maxUint32 = 2 ** 32;

    exports.encoder = new TextEncoder();
    exports.decoder = new TextDecoder();

    // 连接多个Uint8Array
    function concatArrays() {
        var arrays = [];
        for (var i = 0; i < arguments.length; i++) {
            arrays[i] = arguments[i]
        }

        var totalLength = arrays.reduce(function(sum, arr) {
                return sum + arr.length
            }, 0),
            result = new Uint8Array(totalLength),
            offset = 0;

        for (var array of arrays) {
            result.set(array, offset);
            offset += array.length
        }

        return result
    }

    // 密码转盐值 (Password to Salt)
    function passwordToSalt(password, salt) {
        return concatArrays(exports.encoder.encode(password), new Uint8Array([0]), salt)
    }

    // 64位大端序
    function uint64BigEndian(value) {
        var high = Math.floor(value / maxUint32),
            low = value % maxUint32,
            result = new Uint8Array(8);

        writeUint32BE(result, high, 0);
        writeUint32BE(result, low, 4);
        return result
    }

    // 32位大端序
    function uint32BigEndian(value) {
        var result = new Uint8Array(4);
        writeUint32BE(result, value);
        return result
    }

    // 长度和输入数据组合
    function lengthAndInput(input) {
        return concatArrays(uint32BigEndian(input.length), input)
    }

    // 连接KDF (Key Derivation Function)
    async function concatKdf(sharedKey, keyLength, algorithmId) {
        var iterations = Math.ceil((keyLength >> 3) / 32),
            result = new Uint8Array(32 * iterations);

        for (let i = 0; i < iterations; i++) {
            var input = new Uint8Array(4 + sharedKey.length + algorithmId.length);
            input.set(uint32BigEndian(i + 1));
            input.set(sharedKey, 4);
            input.set(algorithmId, 4 + sharedKey.length);
            result.set(await hasherModule.default("sha256", input), 32 * i)
        }

        return result.slice(0, keyLength >> 3)
    }

    // 写入32位大端序整数
    function writeUint32BE(buffer, value, offset) {
        offset = offset || 0;
        if (value < 0 || maxUint32 <= value) {
            throw new RangeError("value must be >= 0 and <= ".concat(maxUint32 - 1, ". Received ") + value)
        }
        buffer.set([value >>> 24, value >>> 16, value >>> 8, 255 & value], offset)
    }
});

// ============================================================================
// Base64编码工具模块
// ============================================================================

// Base64编码解码工具
var base64Utils = moduleWrapper((exports, module) => {  // lu -> base64Utils
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.decode = exports.encode = exports.encodeBase64 = exports.decodeBase64 = void 0;

    var Buffer = require("buffer").Buffer,
        byteUtilsModule = byteUtils();

    // Base64URL编码
    exports.encode = function(data) {
        return Buffer.from(data).toString("base64url")
    };

    // Base64解码
    exports.decodeBase64 = function(data) {
        return new Uint8Array(Buffer.from(data, "base64"))
    };

    // Base64编码
    exports.encodeBase64 = function(data) {
        return Buffer.from(data).toString("base64")
    };

    // Base64URL解码
    exports.decode = function(data) {
        return new Uint8Array(Buffer.from(normalizeInput(data), "base64url"))
    };

    // 标准化输入数据
    function normalizeInput(input) {
        var normalized = input;
        if (normalized instanceof Uint8Array) {
            normalized = byteUtilsModule.decoder.decode(normalized)
        }
        return normalized
    }
});

// ============================================================================
// JOSE错误类模块
// ============================================================================

// JOSE (JSON Object Signing and Encryption) 错误类定义
var joseErrors = moduleWrapper((exports, module) => {  // Zr -> joseErrors
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.JWSSignatureVerificationFailed = exports.JWKSTimeout = exports.JWKSMultipleMatchingKeys =
    exports.JWKSNoMatchingKey = exports.JWKSInvalid = exports.JWKInvalid = exports.JWTInvalid =
    exports.JWSInvalid = exports.JWEInvalid = exports.JWEDecryptionFailed = exports.JOSENotSupported =
    exports.JOSEAlgNotAllowed = exports.JWTExpired = exports.JWTClaimValidationFailed = exports.JOSEError = void 0;

    // 基础JOSE错误类
    var JOSEError = /** @class */ (function(_super) {
        function JOSEError(message, options) {
            var _this = _super.call(this, message, options) || this;
            _this.code = "ERR_JOSE_GENERIC";
            _this.name = _this.constructor.name;
            if (Error.captureStackTrace) {
                Error.captureStackTrace(_this, _this.constructor)
            }
            return _this
        }
        JOSEError.code = "ERR_JOSE_GENERIC";
        return JOSEError
    }(Error));

    exports.JOSEError = JOSEError;

    // JWT声明验证失败错误
    var JWTClaimValidationFailed = /** @class */ (function(_super) {
        function JWTClaimValidationFailed(message, payload, claim, reason) {
            claim = void 0 === claim ? "unspecified" : claim;
            reason = void 0 === reason ? "unspecified" : reason;

            var _this = _super.call(this, message, {
                cause: {
                    claim: claim,
                    reason: reason,
                    payload: payload
                }
            }) || this;

            _this.code = "ERR_JWT_CLAIM_VALIDATION_FAILED";
            _this.claim = claim;
            _this.reason = reason;
            _this.payload = payload;
            return _this
        }
        JWTClaimValidationFailed.code = "ERR_JWT_CLAIM_VALIDATION_FAILED";
        return JWTClaimValidationFailed
    }(JOSEError));

    exports.JWTClaimValidationFailed = JWTClaimValidationFailed;

    // JWT过期错误
    var JWTExpired = /** @class */ (function(_super) {
        function JWTExpired(message, payload, claim, reason) {
            claim = void 0 === claim ? "unspecified" : claim;
            reason = void 0 === reason ? "unspecified" : reason;

            var _this = _super.call(this, message, {
                cause: {
                    claim: claim,
                    reason: reason,
                    payload: payload
                }
            }) || this;

            _this.code = "ERR_JWT_EXPIRED";
            _this.claim = claim;
            _this.reason = reason;
            _this.payload = payload;
            return _this
        }
        JWTExpired.code = "ERR_JWT_EXPIRED";
        return JWTExpired
    }(JOSEError));

    exports.JWTExpired = JWTExpired;

    // JOSE算法不允许错误
    var JOSEAlgNotAllowed = /** @class */ (function(_super) {
        function JOSEAlgNotAllowed() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.code = "ERR_JOSE_ALG_NOT_ALLOWED";
            return _this
        }
        JOSEAlgNotAllowed.code = "ERR_JOSE_ALG_NOT_ALLOWED";
        return JOSEAlgNotAllowed
    }(JOSEError));

    exports.JOSEAlgNotAllowed = JOSEAlgNotAllowed;

    // JOSE不支持错误
    var JOSENotSupported = /** @class */ (function(_super) {
        function JOSENotSupported() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.code = "ERR_JOSE_NOT_SUPPORTED";
            return _this
        }
        JOSENotSupported.code = "ERR_JOSE_NOT_SUPPORTED";
        return JOSENotSupported
    }(JOSEError));

    exports.JOSENotSupported = JOSENotSupported;

    // JWE解密失败错误
    var JWEDecryptionFailed = /** @class */ (function(_super) {
        function JWEDecryptionFailed(message, options) {
            message = void 0 === message ? "decryption operation failed" : message;
            var _this = _super.call(this, message, options) || this;
            _this.code = "ERR_JWE_DECRYPTION_FAILED";
            return _this
        }
        JWEDecryptionFailed.code = "ERR_JWE_DECRYPTION_FAILED";
        return JWEDecryptionFailed
    }(JOSEError));

    exports.JWEDecryptionFailed = JWEDecryptionFailed;

    // JWE无效错误
    var JWEInvalid = /** @class */ (function(_super) {
        function JWEInvalid() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.code = "ERR_JWE_INVALID";
            return _this
        }
        JWEInvalid.code = "ERR_JWE_INVALID";
        return JWEInvalid
    }(JOSEError));

    exports.JWEInvalid = JWEInvalid;

    // JWS无效错误
    var JWSInvalid = /** @class */ (function(_super) {
        function JWSInvalid() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.code = "ERR_JWS_INVALID";
            return _this
        }
        JWSInvalid.code = "ERR_JWS_INVALID";
        return JWSInvalid
    }(JOSEError));

    exports.JWSInvalid = JWSInvalid;

    // JWT无效错误
    var JWTInvalid = /** @class */ (function(_super) {
        function JWTInvalid() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.code = "ERR_JWT_INVALID";
            return _this
        }
        JWTInvalid.code = "ERR_JWT_INVALID";
        return JWTInvalid
    }(JOSEError));

    exports.JWTInvalid = JWTInvalid;

    // JWK无效错误
    var JWKInvalid = /** @class */ (function(_super) {
        function JWKInvalid() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.code = "ERR_JWK_INVALID";
            return _this
        }
        JWKInvalid.code = "ERR_JWK_INVALID";
        return JWKInvalid
    }(JOSEError));

    exports.JWKInvalid = JWKInvalid;

    // JWKS无效错误
    var JWKSInvalid = /** @class */ (function(_super) {
        function JWKSInvalid() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.code = "ERR_JWKS_INVALID";
            return _this
        }
        JWKSInvalid.code = "ERR_JWKS_INVALID";
        return JWKSInvalid
    }(JOSEError));

    exports.JWKSInvalid = JWKSInvalid;

    // JWKS无匹配密钥错误
    var JWKSNoMatchingKey = /** @class */ (function(_super) {
        function JWKSNoMatchingKey(message, options) {
            message = void 0 === message ? "no applicable key found in the JSON Web Key Set" : message;
            var _this = _super.call(this, message, options) || this;
            _this.code = "ERR_JWKS_NO_MATCHING_KEY";
            return _this
        }
        JWKSNoMatchingKey.code = "ERR_JWKS_NO_MATCHING_KEY";
        return JWKSNoMatchingKey
    }(JOSEError));

    exports.JWKSNoMatchingKey = JWKSNoMatchingKey;

    // JWKS多个匹配密钥错误
    var JWKSMultipleMatchingKeys = /** @class */ (function(_super) {
        function JWKSMultipleMatchingKeys(message, options) {
            message = void 0 === message ? "multiple matching keys found in the JSON Web Key Set" : message;
            var _this = _super.call(this, message, options) || this;
            _this.code = "ERR_JWKS_MULTIPLE_MATCHING_KEYS";
            return _this
        }
        JWKSMultipleMatchingKeys.code = "ERR_JWKS_MULTIPLE_MATCHING_KEYS";
        return JWKSMultipleMatchingKeys
    }(JOSEError));

    exports.JWKSMultipleMatchingKeys = JWKSMultipleMatchingKeys;

    // JWKS超时错误
    var JWKSTimeout = /** @class */ (function(_super) {
        function JWKSTimeout(message, options) {
            message = void 0 === message ? "request timed out" : message;
            var _this = _super.call(this, message, options) || this;
            _this.code = "ERR_JWKS_TIMEOUT";
            return _this
        }
        JWKSTimeout.code = "ERR_JWKS_TIMEOUT";
        return JWKSTimeout
    }(JOSEError));

    exports.JWKSTimeout = JWKSTimeout;

    // JWS签名验证失败错误
    var JWSSignatureVerificationFailed = /** @class */ (function(_super) {
        function JWSSignatureVerificationFailed(message, options) {
            message = void 0 === message ? "signature verification failed" : message;
            var _this = _super.call(this, message, options) || this;
            _this.code = "ERR_JWS_SIGNATURE_VERIFICATION_FAILED";
            return _this
        }
        JWSSignatureVerificationFailed.code = "ERR_JWS_SIGNATURE_VERIFICATION_FAILED";
        return JWSSignatureVerificationFailed
    }(JOSEError));

    exports.JWSSignatureVerificationFailed = JWSSignatureVerificationFailed
});

// ============================================================================
// 随机数生成模块
// ============================================================================

// 随机数生成器 - 使用Node.js crypto模块
var randomGenerator = moduleWrapper((exports, module) => {  // wx -> randomGenerator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.default = void 0;

    var crypto = require("crypto");

    Object.defineProperty(exports, "default", {
        enumerable: true,
        get: function() {
            return crypto.randomFillSync
        }
    })
});

// ============================================================================
// 初始化向量生成模块
// ============================================================================

// 初始化向量生成器 - 用于加密算法
var ivGenerator = moduleWrapper((exports, module) => {  // jne -> ivGenerator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.bitLength = getBitLength;

    var joseErrorsModule = joseErrors(),
        randomModule = randomGenerator();

    // 获取算法对应的位长度
    function getBitLength(algorithm) {
        switch (algorithm) {
            case "A128GCM":
            case "A128GCMKW":
            case "A192GCM":
            case "A192GCMKW":
            case "A256GCM":
            case "A256GCMKW":
                return 96;
            case "A128CBC-HS256":
            case "A192CBC-HS384":
            case "A256CBC-HS512":
                return 128;
            default:
                throw new joseErrorsModule.JOSENotSupported("Unsupported JWE Algorithm: " + algorithm)
        }
    }

    // 生成初始化向量
    exports.default = function(algorithm) {
        return randomModule.default(new Uint8Array(getBitLength(algorithm) >> 3))
    }
});

// ============================================================================
// 加密算法检查模块
// ============================================================================

// 加密算法检查器 - 验证算法支持性
var algorithmChecker = moduleWrapper((exports, module) => {  // Jne -> algorithmChecker
    Object.defineProperty(exports, "__esModule", {
        value: true
    })

    // 这个模块主要用于算法验证和检查
    // 具体实现会根据需要的加密算法进行扩展
});

// ============================================================================
// 初始化向量验证模块
// ============================================================================

// 初始化向量验证器 - 验证IV长度的正确性
var ivValidator = moduleWrapper((exports, module) => {  // 从2200行开始的模块
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var joseErrorsModule = joseErrors(),
        ivGeneratorModule = ivGenerator();

    exports.default = function(algorithm, iv) {
        if (iv.length << 3 !== ivGeneratorModule.bitLength(algorithm)) {
            throw new joseErrorsModule.JWEInvalid("Invalid Initialization Vector length")
        }
    }
});

// ============================================================================
// 密钥对象检测模块
// ============================================================================

// 密钥对象检测器 - 检测是否为Node.js KeyObject
var keyObjectDetector = moduleWrapper((exports, module) => {  // vp -> keyObjectDetector
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var util = require("util");

    exports.default = function(obj) {
        return util.types.isKeyObject(obj)
    }
});

// ============================================================================
// 内容加密密钥验证模块
// ============================================================================

// 内容加密密钥验证器 - 验证CEK的长度和类型
var cekValidator = moduleWrapper((exports, module) => {  // tse -> cekValidator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var joseErrorsModule = joseErrors(),
        keyObjectModule = keyObjectDetector();

    exports.default = function(algorithm, cek) {
        var expectedBits;

        // 根据算法确定期望的密钥长度
        switch (algorithm) {
            case "A128CBC-HS256":
            case "A192CBC-HS384":
            case "A256CBC-HS512":
                expectedBits = parseInt(algorithm.slice(-3), 10);
                break;
            case "A128GCM":
            case "A192GCM":
            case "A256GCM":
                expectedBits = parseInt(algorithm.slice(1, 4), 10);
                break;
            default:
                throw new joseErrorsModule.JOSENotSupported("Content Encryption Algorithm ".concat(algorithm, " is not supported either by JOSE or your javascript runtime"))
        }

        if (cek instanceof Uint8Array) {
            // 验证Uint8Array密钥长度
            var actualBits = cek.byteLength << 3;
            if (actualBits !== expectedBits) {
                throw new joseErrorsModule.JWEInvalid("Invalid Content Encryption Key length. Expected ".concat(expectedBits, " bits, got ").concat(actualBits, " bits"))
            }
        } else {
            // 验证KeyObject密钥
            if (!keyObjectModule.default(cek) || "secret" !== cek.type) {
                throw new TypeError("Invalid Content Encryption Key type")
            }

            var actualBits = cek.symmetricKeySize << 3;
            if (actualBits !== expectedBits) {
                throw new joseErrorsModule.JWEInvalid("Invalid Content Encryption Key length. Expected ".concat(expectedBits, " bits, got ").concat(actualBits, " bits"))
            }
        }
    }
});

// ============================================================================
// 时间安全比较模块
// ============================================================================

// 时间安全比较器 - 防止时序攻击
var timingSafeEqual = moduleWrapper((exports, module) => {  // KSe -> timingSafeEqual
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var crypto = require("crypto");

    exports.default = crypto.timingSafeEqual
});

// ============================================================================
// HMAC计算模块
// ============================================================================

// HMAC计算器 - 用于消息认证码计算
var hmacCalculator = moduleWrapper((exports, module) => {  // sse -> hmacCalculator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var crypto = require("crypto"),
        byteUtilsModule = byteUtils();

    exports.default = function(associatedData, iv, ciphertext, bitLength, hmacKey, tagLength) {
        // 构建HMAC输入数据
        var hmacInput = byteUtilsModule.concat(
            associatedData,
            iv,
            ciphertext,
            byteUtilsModule.uint64be(associatedData.length << 3)
        );

        // 计算HMAC
        var hmac = crypto.createHmac("sha" + bitLength, hmacKey);
        hmac.update(hmacInput);

        return hmac.digest().slice(0, tagLength >> 3)
    }
});

// ============================================================================
// Web Crypto API模块
// ============================================================================

// Web Crypto API包装器 - 提供统一的加密接口
var webCrypto = moduleWrapper((exports, module) => {  // yp -> webCrypto
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.isCryptoKey = void 0;

    var crypto = require("crypto"),
        util = require("util"),
        webcrypto = crypto.webcrypto;

    exports.default = webcrypto;

    exports.isCryptoKey = function(obj) {
        return util.types.isCryptoKey(obj)
    }
});

// ============================================================================
// 加密密钥验证模块
// ============================================================================

// 加密密钥验证器 - 验证CryptoKey是否支持特定操作
var cryptoKeyValidator = moduleWrapper((exports, module) => {  // Z3 -> cryptoKeyValidator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.checkSigCryptoKey = checkSignatureCryptoKey;
    exports.checkEncCryptoKey = checkEncryptionCryptoKey;

    // 创建不支持操作的错误
    function createUnsupportedError(expected, property) {
        property = void 0 === property ? "algorithm.name" : property;
        return new TypeError("CryptoKey does not support this operation, its ".concat(property, " must be ") + expected)
    }

    // 检查算法名称是否匹配
    function isAlgorithmName(algorithm, name) {
        return algorithm.name === name
    }

    // 获取哈希算法的位数
    function getHashBits(hashAlgorithm) {
        return parseInt(hashAlgorithm.name.slice(4), 10)
    }

    // 检查密钥用途
    function checkKeyUsages(key, requiredUsages) {
        if (requiredUsages.length && !requiredUsages.some(function(usage) {
            return key.usages.includes(usage)
        })) {
            var errorMessage = "CryptoKey does not support this operation, its usages must include ";

            if (2 < requiredUsages.length) {
                var lastUsage = requiredUsages.pop();
                errorMessage += "one of ".concat(requiredUsages.join(", "), ", or ").concat(lastUsage, ".")
            } else if (2 === requiredUsages.length) {
                errorMessage += "one of ".concat(requiredUsages[0], " or ").concat(requiredUsages[1], ".")
            } else {
                errorMessage += requiredUsages[0] + "."
            }

            throw new TypeError(errorMessage)
        }
    }

    // 检查签名用CryptoKey
    function checkSignatureCryptoKey(key, algorithm) {
        var requiredUsages = [];
        for (var i = 2; i < arguments.length; i++) {
            requiredUsages[i - 2] = arguments[i]
        }

        switch (algorithm) {
            case "HS256":
            case "HS384":
            case "HS512":
                if (!isAlgorithmName(key.algorithm, "HMAC")) {
                    throw createUnsupportedError("HMAC")
                }
                var expectedBits = parseInt(algorithm.slice(2), 10);
                if (getHashBits(key.algorithm.hash) !== expectedBits) {
                    throw createUnsupportedError("SHA-" + expectedBits, "algorithm.hash")
                }
                break;

            case "RS256":
            case "RS384":
            case "RS512":
                if (!isAlgorithmName(key.algorithm, "RSASSA-PKCS1-v1_5")) {
                    throw createUnsupportedError("RSASSA-PKCS1-v1_5")
                }
                var expectedBits = parseInt(algorithm.slice(2), 10);
                if (getHashBits(key.algorithm.hash) !== expectedBits) {
                    throw createUnsupportedError("SHA-" + expectedBits, "algorithm.hash")
                }
                break;

            case "PS256":
            case "PS384":
            case "PS512":
                if (!isAlgorithmName(key.algorithm, "RSA-PSS")) {
                    throw createUnsupportedError("RSA-PSS")
                }
                var expectedBits = parseInt(algorithm.slice(2), 10);
                if (getHashBits(key.algorithm.hash) !== expectedBits) {
                    throw createUnsupportedError("SHA-" + expectedBits, "algorithm.hash")
                }
                break;

            case "EdDSA":
                if ("Ed25519" !== key.algorithm.name && "Ed448" !== key.algorithm.name) {
                    throw createUnsupportedError("Ed25519 or Ed448")
                }
                break;

            case "Ed25519":
                if (!isAlgorithmName(key.algorithm, "Ed25519")) {
                    throw createUnsupportedError("Ed25519")
                }
                break;

            case "ES256":
            case "ES384":
            case "ES512":
                if (!isAlgorithmName(key.algorithm, "ECDSA")) {
                    throw createUnsupportedError("ECDSA")
                }

                var expectedCurve = getExpectedCurve(algorithm);
                if (key.algorithm.namedCurve !== expectedCurve) {
                    throw createUnsupportedError(expectedCurve, "algorithm.namedCurve")
                }
                break;

            default:
                throw new TypeError("CryptoKey does not support this operation")
        }

        checkKeyUsages(key, requiredUsages)
    }

    // 获取期望的椭圆曲线
    function getExpectedCurve(algorithm) {
        switch (algorithm) {
            case "ES256":
                return "P-256";
            case "ES384":
                return "P-384";
            case "ES512":
                return "P-521";
            default:
                throw new Error("unreachable")
        }
    }

    // 检查加密用CryptoKey
    function checkEncryptionCryptoKey(key, algorithm) {
        var requiredUsages = [];
        for (var i = 2; i < arguments.length; i++) {
            requiredUsages[i - 2] = arguments[i]
        }

        switch (algorithm) {
            case "A128GCM":
            case "A192GCM":
            case "A256GCM":
                if (!isAlgorithmName(key.algorithm, "AES-GCM")) {
                    throw createUnsupportedError("AES-GCM")
                }
                var expectedLength = parseInt(algorithm.slice(1, 4), 10);
                if (key.algorithm.length !== expectedLength) {
                    throw createUnsupportedError(expectedLength, "algorithm.length")
                }
                break;

            case "A128KW":
            case "A192KW":
            case "A256KW":
                if (!isAlgorithmName(key.algorithm, "AES-KW")) {
                    throw createUnsupportedError("AES-KW")
                }
                var expectedLength = parseInt(algorithm.slice(1, 4), 10);
                if (key.algorithm.length !== expectedLength) {
                    throw createUnsupportedError(expectedLength, "algorithm.length")
                }
                break;

            case "ECDH":
                switch (key.algorithm.name) {
                    case "ECDH":
                    case "X25519":
                    case "X448":
                        break;
                    default:
                        throw createUnsupportedError("ECDH, X25519, or X448")
                }
                break;

            case "PBES2-HS256+A128KW":
            case "PBES2-HS384+A192KW":
            case "PBES2-HS512+A256KW":
                if (!isAlgorithmName(key.algorithm, "PBKDF2")) {
                    throw createUnsupportedError("PBKDF2")
                }
                break;

            case "RSA-OAEP":
            case "RSA-OAEP-256":
            case "RSA-OAEP-384":
            case "RSA-OAEP-512":
                if (!isAlgorithmName(key.algorithm, "RSA-OAEP")) {
                    throw createUnsupportedError("RSA-OAEP")
                }
                var expectedBits = parseInt(algorithm.slice(9), 10) || 1;
                if (getHashBits(key.algorithm.hash) !== expectedBits) {
                    throw createUnsupportedError("SHA-" + expectedBits, "algorithm.hash")
                }
                break;

            default:
                throw new TypeError("CryptoKey does not support this operation")
        }

        checkKeyUsages(key, requiredUsages)
    }
});

// ============================================================================
// 密钥类型错误处理模块
// ============================================================================

// 密钥类型错误处理器 - 生成详细的密钥类型错误信息
var keyTypeErrorHandler = moduleWrapper((exports, module) => {  // Ep -> keyTypeErrorHandler
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.withAlg = withAlgorithm;
    exports.default = createKeyTypeError;

    // 创建详细的错误信息
    function createDetailedErrorMessage(baseMessage, key) {
        var types = [];
        for (var i = 2; i < arguments.length; i++) {
            types[i - 2] = arguments[i]
        }

        // 过滤掉空值
        types = types.filter(Boolean);

        // 构建类型描述
        if (2 < types.length) {
            var lastType = types.pop();
            baseMessage += "one of type ".concat(types.join(", "), ", or ").concat(lastType, ".")
        } else if (2 === types.length) {
            baseMessage += "one of type ".concat(types[0], " or ").concat(types[1], ".")
        } else {
            baseMessage += "of type ".concat(types[0], ".")
        }

        // 添加实际接收到的类型信息
        if (null == key) {
            baseMessage += " Received " + key
        } else if ("function" == typeof key && key.name) {
            baseMessage += " Received function " + key.name
        } else if ("object" == typeof key && null != key && (null == key ? void 0 : key.constructor) && key.constructor.name) {
            baseMessage += " Received an instance of " + key.constructor.name
        }

        return baseMessage
    }

    // 为特定算法创建密钥错误信息
    function withAlgorithm(algorithm, key) {
        var types = [];
        for (var i = 2; i < arguments.length; i++) {
            types[i - 2] = arguments[i]
        }

        return createDetailedErrorMessage.apply(void 0,
            ["Key for the ".concat(algorithm, " algorithm must be "), key].concat(types))
    }

    // 创建通用密钥类型错误
    function createKeyTypeError(key) {
        var types = [];
        for (var i = 1; i < arguments.length; i++) {
            types[i - 1] = arguments[i]
        }

        return createDetailedErrorMessage.apply(void 0,
            ["Key must be "].concat([key], types))
    }
});

// ============================================================================
// 加密算法支持检测模块
// ============================================================================

// 加密算法支持检测器 - 检查Node.js是否支持特定加密算法
var cipherSupport = moduleWrapper((exports, module) => {  // Uz -> cipherSupport
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var crypto = require("crypto"),
        supportedCiphers;

    exports.default = function(cipherName) {
        // 懒加载支持的加密算法列表
        if (!supportedCiphers) {
            supportedCiphers = new Set(crypto.getCiphers())
        }
        return supportedCiphers.has(cipherName)
    }
});

// ============================================================================
// 密钥类型检测模块
// ============================================================================

// 密钥类型检测器 - 检测各种密钥类型
var keyTypeDetector = moduleWrapper((exports, module) => {  // wp -> keyTypeDetector
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.types = void 0;

    var webCryptoModule = webCrypto(),
        keyObjectModule = keyObjectDetector();

    // 检测是否为支持的密钥类型
    exports.default = function(key) {
        return keyObjectModule.default(key) || webCryptoModule.isCryptoKey(key)
    };

    // 支持的密钥类型列表
    var supportedTypes = ["KeyObject"];
    exports.types = supportedTypes;

    // 如果环境支持CryptoKey，添加到类型列表
    if (globalThis.CryptoKey || (null == webCryptoModule.default ? void 0 : webCryptoModule.default.CryptoKey)) {
        supportedTypes.push("CryptoKey")
    }
});

// ============================================================================
// JWE内容解密模块
// ============================================================================

// JWE内容解密器 - 解密JWE内容
var jweContentDecryptor = moduleWrapper((exports, module) => {  // cse -> jweContentDecryptor
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var crypto = require("crypto"),
        algorithmCheckerModule = algorithmChecker(),
        cekValidatorModule = cekValidator(),
        byteUtilsModule = byteUtils(),
        joseErrorsModule = joseErrors(),
        timingSafeEqualModule = timingSafeEqual(),
        hmacCalculatorModule = hmacCalculator(),
        webCryptoModule = webCrypto(),
        cryptoKeyValidatorModule = cryptoKeyValidator(),
        keyObjectModule = keyObjectDetector(),
        keyTypeErrorModule = keyTypeErrorHandler(),
        cipherSupportModule = cipherSupport(),
        keyTypeModule = keyTypeDetector();

    exports.default = function(algorithm, cek, ciphertext, iv, tag, additionalData) {
        var keyObject;

        // 处理CryptoKey类型的密钥
        if (webCryptoModule.isCryptoKey(cek)) {
            cryptoKeyValidatorModule.checkEncCryptoKey(cek, algorithm, "decrypt");
            keyObject = crypto.KeyObject.from(cek)
        } else {
            // 验证密钥类型
            if (!(cek instanceof Uint8Array || keyObjectModule.default(cek))) {
                throw new TypeError(keyTypeErrorModule.default.apply(void 0,
                    [cek].concat(keyTypeModule.types, ["Uint8Array"])))
            }
            keyObject = cek
        }

        // 验证必需的参数
        if (!iv) {
            throw new joseErrorsModule.JWEInvalid("JWE Initialization Vector missing")
        }
        if (!tag) {
            throw new joseErrorsModule.JWEInvalid("JWE Authentication Tag missing")
        }

        // 验证CEK和IV
        cekValidatorModule.default(algorithm, keyObject);
        algorithmCheckerModule.default(algorithm, iv);

        switch (algorithm) {
            case "A128CBC-HS256":
            case "A192CBC-HS384":
            case "A256CBC-HS512":
                return decryptCBCHS(algorithm, keyObject, ciphertext, iv, tag, additionalData);

            case "A128GCM":
            case "A192GCM":
            case "A256GCM":
                return decryptGCM(algorithm, keyObject, ciphertext, iv, tag, additionalData);

            default:
                throw new joseErrorsModule.JOSENotSupported("Unsupported JWE Content Encryption Algorithm")
        }
    };

    // 解密CBC+HMAC模式
    function decryptCBCHS(algorithm, cek, ciphertext, iv, tag, additionalData) {
        var keySize = parseInt(algorithm.slice(1, 4), 10),
            exportedKey = keyObjectModule.default(cek) ? cek.export() : cek,
            encKey = exportedKey.subarray(keySize >> 3),
            macKey = exportedKey.subarray(0, keySize >> 3),
            hashSize = parseInt(algorithm.slice(-3), 10),
            cipherName = "aes-".concat(keySize, "-cbc");

        if (!cipherSupportModule.default(cipherName)) {
            throw new joseErrorsModule.JOSENotSupported("alg ".concat(algorithm, " is not supported by your javascript runtime"))
        }

        // 验证HMAC标签
        var expectedTag = hmacCalculatorModule.default(additionalData, iv, ciphertext, hashSize, macKey, keySize),
            isValidTag;

        try {
            isValidTag = timingSafeEqualModule.default(tag, expectedTag)
        } catch (error) {
            // HMAC验证失败
        }

        if (isValidTag) {
            var plaintext;
            try {
                var decipher = crypto.createDecipheriv(cipherName, encKey, iv);
                plaintext = byteUtilsModule.concat(decipher.update(ciphertext), decipher.final())
            } catch (error) {
                // 解密失败
            }
            if (plaintext) {
                return plaintext
            }
        }

        throw new joseErrorsModule.JWEDecryptionFailed()
    }

    // 解密GCM模式
    function decryptGCM(algorithm, cek, ciphertext, iv, tag, additionalData) {
        var cipherName = "aes-".concat(parseInt(algorithm.slice(1, 4), 10), "-gcm");

        if (!cipherSupportModule.default(cipherName)) {
            throw new joseErrorsModule.JOSENotSupported("alg ".concat(algorithm, " is not supported by your javascript runtime"))
        }

        try {
            var decipher = crypto.createDecipheriv(cipherName, cek, iv, {
                authTagLength: 16
            });

            decipher.setAuthTag(tag);

            if (additionalData.byteLength) {
                decipher.setAAD(additionalData, {
                    plaintextLength: ciphertext.length
                })
            }

            var plaintext = decipher.update(ciphertext);
            decipher.final();

            return plaintext
        } catch (error) {
            throw new joseErrorsModule.JWEDecryptionFailed()
        }
    }
});

// ============================================================================
// 对象属性检查模块
// ============================================================================

// 对象属性检查器 - 检查多个对象是否有重复属性
var objectPropertyChecker = moduleWrapper((exports, module) => {  // t6 -> objectPropertyChecker
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    exports.default = function() {
        var objects = [];
        for (var i = 0; i < arguments.length; i++) {
            objects[i] = arguments[i]
        }

        // 过滤掉空值
        objects = objects.filter(Boolean);

        if (0 === objects.length || 1 === objects.length) {
            return true
        }

        var allKeys;

        for (var obj of objects) {
            var keys = Object.keys(obj);

            if (allKeys && 0 !== allKeys.size) {
                // 检查是否有重复的键
                for (var key of keys) {
                    if (allKeys.has(key)) {
                        return false // 发现重复键
                    }
                    allKeys.add(key)
                }
            } else {
                allKeys = new Set(keys)
            }
        }

        return true
    }
});

// ============================================================================
// 纯对象检测模块
// ============================================================================

// 纯对象检测器 - 检测是否为纯JavaScript对象
var plainObjectDetector = moduleWrapper((exports, module) => {  // wl -> plainObjectDetector
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    exports.default = function(obj) {
        // 检查是否为对象且不为null
        if (!isObject(obj) || "[object Object]" !== Object.prototype.toString.call(obj)) {
            return false
        }

        // 检查原型链
        if (null === Object.getPrototypeOf(obj)) {
            return true
        }

        var proto = obj;
        while (null !== Object.getPrototypeOf(proto)) {
            proto = Object.getPrototypeOf(proto)
        }

        return Object.getPrototypeOf(obj) === proto
    };

    // 辅助函数：检查是否为对象
    function isObject(value) {
        return "object" == typeof value && null !== value
    }
});

// ============================================================================
// AES密钥包装模块
// ============================================================================

// AES密钥包装器 - 实现AES-KW算法
var aesKeyWrapper = moduleWrapper((exports, module) => {  // Hz -> aesKeyWrapper
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.unwrap = unwrapKey;
    exports.wrap = wrapKey;

    var Buffer = require("buffer").Buffer,
        crypto = require("crypto"),
        joseErrorsModule = joseErrors(),
        byteUtilsModule = byteUtils(),
        webCryptoModule = webCrypto(),
        cryptoKeyValidatorModule = cryptoKeyValidator(),
        keyObjectModule = keyObjectDetector(),
        keyTypeErrorModule = keyTypeErrorHandler(),
        cipherSupportModule = cipherSupport(),
        keyTypeModule = keyTypeDetector();

    // 验证密钥大小
    function validateKeySize(keyObject, algorithm) {
        if (keyObject.symmetricKeySize << 3 !== parseInt(algorithm.slice(1, 4), 10)) {
            throw new TypeError("Invalid key size for alg: " + algorithm)
        }
    }

    // 准备密钥对象
    function prepareKeyObject(key, algorithm, operation) {
        if (keyObjectModule.default(key)) {
            return key
        }
        if (key instanceof Uint8Array) {
            return crypto.createSecretKey(key)
        }
        if (webCryptoModule.isCryptoKey(key)) {
            cryptoKeyValidatorModule.checkEncCryptoKey(key, algorithm, operation);
            return crypto.KeyObject.from(key)
        }

        throw new TypeError(keyTypeErrorModule.default.apply(void 0,
            [key].concat(keyTypeModule.types, ["Uint8Array"])))
    }

    // 包装密钥
    function wrapKey(algorithm, kek, cek) {
        var cipherName = "aes".concat(parseInt(algorithm.slice(1, 4), 10), "-wrap");

        if (cipherSupportModule.default(cipherName)) {
            var keyObject = prepareKeyObject(kek, algorithm, "wrapKey");
            validateKeySize(keyObject, algorithm);

            var cipher = crypto.createCipheriv(cipherName, keyObject, Buffer.alloc(8, 166));
            return byteUtilsModule.concat(cipher.update(cek), cipher.final())
        }

        throw new joseErrorsModule.JOSENotSupported("alg ".concat(algorithm, " is not supported either by JOSE or your javascript runtime"))
    }

    // 解包密钥
    function unwrapKey(algorithm, kek, encryptedKey) {
        var cipherName = "aes".concat(parseInt(algorithm.slice(1, 4), 10), "-wrap");

        if (cipherSupportModule.default(cipherName)) {
            var keyObject = prepareKeyObject(kek, algorithm, "unwrapKey");
            validateKeySize(keyObject, algorithm);

            var decipher = crypto.createDecipheriv(cipherName, keyObject, Buffer.alloc(8, 166));
            return byteUtilsModule.concat(decipher.update(encryptedKey), decipher.final())
        }

        throw new joseErrorsModule.JOSENotSupported("alg ".concat(algorithm, " is not supported either by JOSE or your javascript runtime"))
    }
});

// ============================================================================
// JWK类型检测模块
// ============================================================================

// JWK类型检测器 - 检测各种JWK类型
var jwkTypeDetector = moduleWrapper((exports, module) => {  // xx -> jwkTypeDetector
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.isJWK = isJWK;
    exports.isPrivateJWK = isPrivateJWK;
    exports.isPublicJWK = isPublicJWK;
    exports.isSecretJWK = isSecretJWK;

    var plainObjectModule = plainObjectDetector();

    // 检测是否为JWK
    function isJWK(obj) {
        return plainObjectModule.default(obj) && "string" == typeof obj.kty
    }

    // 检测是否为私钥JWK
    function isPrivateJWK(jwk) {
        return "oct" !== jwk.kty && "string" == typeof jwk.d
    }

    // 检测是否为公钥JWK
    function isPublicJWK(jwk) {
        return "oct" !== jwk.kty && "undefined" == typeof jwk.d
    }

    // 检测是否为对称密钥JWK
    function isSecretJWK(jwk) {
        return isJWK(jwk) && "oct" === jwk.kty && "string" == typeof jwk.k
    }
});

// ============================================================================
// 密钥曲线提取模块
// ============================================================================

// 密钥曲线提取器 - 从密钥中提取椭圆曲线信息
var keyCurveExtractor = moduleWrapper((exports, module) => {  // dse -> keyCurveExtractor
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.weakMap = void 0;

    var crypto = require("crypto"),
        joseErrorsModule = joseErrors(),
        webCryptoModule = webCrypto(),
        keyObjectModule = keyObjectDetector(),
        keyTypeErrorModule = keyTypeErrorHandler(),
        keyTypeModule = keyTypeDetector(),
        jwkTypeModule = jwkTypeDetector();

    // 弱映射缓存，用于存储密钥曲线信息
    exports.weakMap = new WeakMap();

    exports.default = function(key, allowedCurves) {
        var keyObject;

        if (webCryptoModule.isCryptoKey(key)) {
            keyObject = crypto.KeyObject.from(key)
        } else {
            if (!keyObjectModule.default(key)) {
                if (jwkTypeModule.isJWK(key)) {
                    return key.crv
                }
                throw new TypeError(keyTypeErrorModule.default.apply(void 0,
                    [key].concat(keyTypeModule.types)))
            }
            keyObject = key
        }

        // 从缓存中获取曲线信息
        var cachedCurve = exports.weakMap.get(keyObject);
        if (cachedCurve) {
            return cachedCurve
        }

        var curve;

        switch (keyObject.asymmetricKeyType) {
            case "ed25519":
            case "ed448":
                curve = "Ed25519" === keyObject.asymmetricKeyType ? "Ed25519" : "Ed448";
                break;

            case "x25519":
            case "x448":
                curve = "X25519" === keyObject.asymmetricKeyType ? "X25519" : "X448";
                break;

            case "ec":
                // 从密钥详情中提取椭圆曲线名称
                var keyDetails = keyObject.asymmetricKeyDetails;
                if (keyDetails && keyDetails.namedCurve) {
                    curve = keyDetails.namedCurve
                }
                break;

            default:
                throw new TypeError("Invalid key type for this operation")
        }

        if (!curve) {
            throw new TypeError("Unable to determine key curve")
        }

        // 验证曲线是否在允许列表中
        if (allowedCurves && !allowedCurves.includes(curve)) {
            throw new joseErrorsModule.JOSENotSupported("Unsupported curve: " + curve)
        }

        // 缓存结果
        exports.weakMap.set(keyObject, curve);

        return curve
    }
});

// ============================================================================
// JWS (JSON Web Signature) 签名模块
// ============================================================================

// JWS 扁平化签名类
var flattenedSignModule = moduleWrapper((exports, module) => {  // uM -> flattenedSignModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.FlattenedSign = void 0;

    var base64UrlModule = base64UrlEncoder(),
        signatureModule = signatureGenerator(),
        headerValidatorModule = headerValidator(),
        joseErrorsModule = joseErrors(),
        stringUtilsModule = stringUtils(),
        keyTypeCheckerModule = keyTypeChecker(),
        criticalHeaderModule = criticalHeaderHandler();

    // JWS 扁平化签名类 - 用于创建单个签名的JWS
    exports.FlattenedSign = class {
        _payload;           // 要签名的载荷数据
        _protectedHeader;   // 受保护的头部信息
        _unprotectedHeader; // 不受保护的头部信息

        constructor(payload) {
            if (!(payload instanceof Uint8Array)) {
                throw new TypeError("payload must be an instance of Uint8Array");
            }
            this._payload = payload;
        }

        // 设置受保护的头部信息
        setProtectedHeader(header) {
            if (this._protectedHeader) {
                throw new TypeError("setProtectedHeader can only be called once");
            }
            this._protectedHeader = header;
            return this;
        }

        // 设置不受保护的头部信息
        setUnprotectedHeader(header) {
            if (this._unprotectedHeader) {
                throw new TypeError("setUnprotectedHeader can only be called once");
            }
            this._unprotectedHeader = header;
            return this;
        }

        // 执行签名操作
        async sign(key, options) {
            // 验证必须设置至少一个头部
            if (!this._protectedHeader && !this._unprotectedHeader) {
                throw new joseErrorsModule.JWSInvalid(
                    "either setProtectedHeader or setUnprotectedHeader must be called before #sign()"
                );
            }

            // 验证头部参数名称不能重复
            if (!headerValidatorModule.default(this._protectedHeader, this._unprotectedHeader)) {
                throw new joseErrorsModule.JWSInvalid(
                    "JWS Protected and JWS Unprotected Header Parameter names must be disjoint"
                );
            }

            // 合并头部信息
            let combinedHeader = {
                ...this._protectedHeader,
                ...this._unprotectedHeader
            };

            // 处理关键头部参数
            let criticalParams = criticalHeaderModule.default(
                joseErrorsModule.JWSInvalid,
                new Map([["b64", true]]),
                options?.crit,
                this._protectedHeader,
                combinedHeader
            );

            // 处理base64url编码标志
            let shouldEncodePayload = true;
            if (criticalParams.has("b64")) {
                shouldEncodePayload = this._protectedHeader.b64;
                if ("boolean" != typeof shouldEncodePayload) {
                    throw new joseErrorsModule.JWSInvalid(
                        'The "b64" (base64url-encode payload) Header Parameter must be a boolean'
                    );
                }
            }

            // 获取算法
            let algorithm = combinedHeader.alg;
            if ("string" != typeof algorithm || !algorithm) {
                throw new joseErrorsModule.JWSInvalid(
                    'JWS "alg" (Algorithm) Header Parameter missing or invalid'
                );
            }

            // 检查密钥类型与算法的兼容性
            keyTypeCheckerModule.checkKeyTypeWithJwk(algorithm, key, "sign");

            // 准备载荷数据
            let payloadData = this._payload;
            if (shouldEncodePayload) {
                payloadData = stringUtilsModule.encoder.encode(
                    base64UrlModule.encode(payloadData)
                );
            }

            // 准备受保护头部数据
            let protectedHeaderData;
            if (this._protectedHeader) {
                protectedHeaderData = stringUtilsModule.encoder.encode(
                    base64UrlModule.encode(JSON.stringify(this._protectedHeader))
                );
            } else {
                protectedHeaderData = stringUtilsModule.encoder.encode("");
            }

            // 构建签名输入数据
            var signingInput = stringUtilsModule.concat(
                protectedHeaderData,
                stringUtilsModule.encoder.encode("."),
                payloadData
            );

            // 执行签名
            let signature = await signatureModule.default(algorithm, key, signingInput);

            // 构建返回结果
            let result = {
                signature: base64UrlModule.encode(signature),
                payload: ""
            };

            // 设置载荷（如果需要编码）
            if (shouldEncodePayload) {
                result.payload = stringUtilsModule.decoder.decode(payloadData);
            }

            // 添加不受保护的头部
            if (this._unprotectedHeader) {
                result.header = this._unprotectedHeader;
            }

            // 添加受保护的头部
            if (this._protectedHeader) {
                result.protected = stringUtilsModule.decoder.decode(protectedHeaderData);
            }

            return result;
        }
    };
});

// JWS 紧凑签名类
var compactSignModule = moduleWrapper((exports, module) => {  // Iie -> compactSignModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.CompactSign = void 0;

    var flattenedSignClass = flattenedSignModule();

    // JWS 紧凑签名类 - 用于创建紧凑格式的JWS
    exports.CompactSign = class {
        _flattened; // 内部使用扁平化签名实例

        constructor(payload) {
            this._flattened = new flattenedSignClass.FlattenedSign(payload);
        }

        // 设置受保护的头部信息
        setProtectedHeader(header) {
            this._flattened.setProtectedHeader(header);
            return this;
        }

        // 执行签名并返回紧凑格式
        async sign(key, options) {
            let result = await this._flattened.sign(key, options);

            // 验证载荷存在（紧凑格式需要）
            if (void 0 === result.payload) {
                throw new TypeError("use the flattened module for creating JWS with b64: false");
            }

            // 返回紧凑格式：header.payload.signature
            return result.protected + `.${result.payload}.` + result.signature;
        }
    };
});

// JWS 通用签名类 - 支持多个签名
var generalSignModule = moduleWrapper((exports, module) => {  // hke -> generalSignModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.GeneralSign = void 0;

    var flattenedSignClass = flattenedSignModule(),
        joseErrorsModule = joseErrors();

    // 签名者类 - 表示单个签名者的配置
    var SignatureBuilder = class {
        parent;             // 父级GeneralSign实例
        protectedHeader;    // 受保护的头部
        unprotectedHeader;  // 不受保护的头部
        options;            // 签名选项
        key;               // 签名密钥

        constructor(parent, key, options) {
            this.parent = parent;
            this.key = key;
            this.options = options;
        }

        // 设置受保护的头部信息
        setProtectedHeader(header) {
            if (this.protectedHeader) {
                throw new TypeError("setProtectedHeader can only be called once");
            }
            this.protectedHeader = header;
            return this;
        }

        // 设置不受保护的头部信息
        setUnprotectedHeader(header) {
            if (this.unprotectedHeader) {
                throw new TypeError("setUnprotectedHeader can only be called once");
            }
            this.unprotectedHeader = header;
            return this;
        }

        // 添加新的签名者
        addSignature(...args) {
            return this.parent.addSignature(...args);
        }

        // 执行签名
        sign(...args) {
            return this.parent.sign(...args);
        }

        // 完成配置，返回父级实例
        done() {
            return this.parent;
        }
    };

    // JWS 通用签名类 - 支持多个签名的JWS
    exports.GeneralSign = class {
        _payload;           // 要签名的载荷数据
        _signatures = [];   // 签名者列表

        constructor(payload) {
            this._payload = payload;
        }

        // 添加签名者
        addSignature(key, options) {
            let signatureBuilder = new SignatureBuilder(this, key, options);
            this._signatures.push(signatureBuilder);
            return signatureBuilder;
        }

        // 执行所有签名
        async sign() {
            if (!this._signatures.length) {
                throw new joseErrorsModule.JWSInvalid("at least one signature must be added");
            }

            // 构建结果对象
            var result = {
                signatures: [],
                payload: ""
            };

            // 为每个签名者执行签名
            for (let index = 0; index < this._signatures.length; index++) {
                var signatureConfig = this._signatures[index];
                var flattenedSigner = new flattenedSignClass.FlattenedSign(this._payload);

                // 设置头部信息
                flattenedSigner.setProtectedHeader(signatureConfig.protectedHeader);
                flattenedSigner.setUnprotectedHeader(signatureConfig.unprotectedHeader);

                // 执行签名
                let {payload: currentPayload, ...signatureResult} = await flattenedSigner.sign(
                    signatureConfig.key,
                    signatureConfig.options
                );

                // 第一个签名设置载荷
                if (0 === index) {
                    result.payload = currentPayload;
                } else if (result.payload !== currentPayload) {
                    // 所有签名的载荷必须一致
                    throw new joseErrorsModule.JWSInvalid(
                        "inconsistent use of JWS Unencoded Payload (RFC7797)"
                    );
                }

                // 添加签名结果
                result.signatures.push(signatureResult);
            }

            return result;
        }
    };
});

// ============================================================================
// JWT (JSON Web Token) 生成模块
// ============================================================================

// JWT 生产者基类
var jwtProducerModule = moduleWrapper((exports, module) => {  // hM -> jwtProducerModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.ProduceJWT = void 0;

    var epochTimeModule = epochTimeConverter(),
        objectValidatorModule = objectValidator(),
        timeSpanModule = timeSpanParser();

    // 数值验证函数
    function validateNumericInput(fieldName, value) {
        if (Number.isFinite(value)) {
            return value;
        }
        throw new TypeError(`Invalid ${fieldName} input`);
    }

    // JWT 生产者类 - 用于构建JWT载荷
    exports.ProduceJWT = class {
        _payload; // JWT载荷数据

        constructor(claims = {}) {
            if (!objectValidatorModule.default(claims)) {
                throw new TypeError("JWT Claims Set MUST be an object");
            }
            this._payload = claims;
        }

        // 设置发行者 (iss)
        setIssuer(issuer) {
            this._payload = {
                ...this._payload,
                iss: issuer
            };
            return this;
        }

        // 设置主题 (sub)
        setSubject(subject) {
            this._payload = {
                ...this._payload,
                sub: subject
            };
            return this;
        }

        // 设置受众 (aud)
        setAudience(audience) {
            this._payload = {
                ...this._payload,
                aud: audience
            };
            return this;
        }

        // 设置JWT ID (jti)
        setJti(jwtId) {
            this._payload = {
                ...this._payload,
                jti: jwtId
            };
            return this;
        }

        // 设置生效时间 (nbf - Not Before)
        setNotBefore(notBefore) {
            if ("number" == typeof notBefore) {
                // 直接使用数值时间戳
                this._payload = {
                    ...this._payload,
                    nbf: validateNumericInput("setNotBefore", notBefore)
                };
            } else if (notBefore instanceof Date) {
                // 使用Date对象
                this._payload = {
                    ...this._payload,
                    nbf: validateNumericInput("setNotBefore", epochTimeModule.default(notBefore))
                };
            } else {
                // 使用时间跨度字符串（如"1h", "30m"等）
                this._payload = {
                    ...this._payload,
                    nbf: epochTimeModule.default(new Date()) + timeSpanModule.default(notBefore)
                };
            }
            return this;
        }

        // 设置过期时间 (exp - Expiration Time)
        setExpirationTime(expirationTime) {
            if ("number" == typeof expirationTime) {
                // 直接使用数值时间戳
                this._payload = {
                    ...this._payload,
                    exp: validateNumericInput("setExpirationTime", expirationTime)
                };
            } else if (expirationTime instanceof Date) {
                // 使用Date对象
                this._payload = {
                    ...this._payload,
                    exp: validateNumericInput("setExpirationTime", epochTimeModule.default(expirationTime))
                };
            } else {
                // 使用时间跨度字符串（如"1h", "30m"等）
                this._payload = {
                    ...this._payload,
                    exp: epochTimeModule.default(new Date()) + timeSpanModule.default(expirationTime)
                };
            }
            return this;
        }

        // 设置签发时间 (iat - Issued At)
        setIssuedAt(issuedAt) {
            if ("undefined" == typeof issuedAt) {
                // 未提供参数，使用当前时间
                this._payload = {
                    ...this._payload,
                    iat: epochTimeModule.default(new Date())
                };
            } else if (issuedAt instanceof Date) {
                // 使用Date对象
                this._payload = {
                    ...this._payload,
                    iat: validateNumericInput("setIssuedAt", epochTimeModule.default(issuedAt))
                };
            } else if ("string" == typeof issuedAt) {
                // 使用时间跨度字符串
                this._payload = {
                    ...this._payload,
                    iat: validateNumericInput("setIssuedAt",
                        epochTimeModule.default(new Date()) + timeSpanModule.default(issuedAt))
                };
            } else {
                // 直接使用数值
                this._payload = {
                    ...this._payload,
                    iat: validateNumericInput("setIssuedAt", issuedAt)
                };
            }
            return this;
        }
    };
});

// JWT 签名类
var signJwtModule = moduleWrapper((exports, module) => {  // gke -> signJwtModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.SignJWT = void 0;

    var compactSignClass = compactSignModule(),
        joseErrorsModule = joseErrors(),
        stringUtilsModule = stringUtils(),
        jwtProducerClass = jwtProducerModule();

    // JWT 签名类 - 继承自JWT生产者，用于创建签名的JWT
    var SignJWTClass = class extends jwtProducerClass.ProduceJWT {
        _protectedHeader; // 受保护的头部信息

        // 设置受保护的头部信息
        setProtectedHeader(header) {
            this._protectedHeader = header;
            return this;
        }

        // 执行JWT签名
        async sign(key, options) {
            // 创建紧凑签名实例，载荷为JSON字符串的UTF-8编码
            var compactSigner = new compactSignClass.CompactSign(
                stringUtilsModule.encoder.encode(JSON.stringify(this._payload))
            );

            // 设置受保护的头部
            compactSigner.setProtectedHeader(this._protectedHeader);

            // 验证JWT不能使用未编码载荷
            if (Array.isArray(this._protectedHeader?.crit) &&
                this._protectedHeader.crit.includes("b64") &&
                false === this._protectedHeader.b64) {
                throw new joseErrorsModule.JWTInvalid("JWTs MUST NOT use unencoded payload");
            }

            // 执行签名并返回紧凑格式JWT
            return compactSigner.sign(key, options);
        }
    };

    exports.SignJWT = SignJWTClass;
});

// JWT 加密类
var encryptJwtModule = moduleWrapper((exports, module) => {  // mke -> encryptJwtModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.EncryptJWT = void 0;

    var compactEncryptClass = compactEncryptModule(),
        stringUtilsModule = stringUtils(),
        jwtProducerClass = jwtProducerModule();

    // JWT 加密类 - 继承自JWT生产者，用于创建加密的JWT
    var EncryptJWTClass = class extends jwtProducerClass.ProduceJWT {
        _cek;                           // 内容加密密钥
        _iv;                            // 初始化向量
        _keyManagementParameters;       // 密钥管理参数
        _protectedHeader;               // 受保护的头部信息
        _replicateIssuerAsHeader;       // 是否将发行者复制到头部
        _replicateSubjectAsHeader;      // 是否将主题复制到头部
        _replicateAudienceAsHeader;     // 是否将受众复制到头部

        // 设置受保护的头部信息
        setProtectedHeader(header) {
            if (this._protectedHeader) {
                throw new TypeError("setProtectedHeader can only be called once");
            }
            this._protectedHeader = header;
            return this;
        }

        // 设置密钥管理参数
        setKeyManagementParameters(parameters) {
            if (this._keyManagementParameters) {
                throw new TypeError("setKeyManagementParameters can only be called once");
            }
            this._keyManagementParameters = parameters;
            return this;
        }

        // 设置内容加密密钥
        setContentEncryptionKey(key) {
            if (this._cek) {
                throw new TypeError("setContentEncryptionKey can only be called once");
            }
            this._cek = key;
            return this;
        }

        // 设置初始化向量
        setInitializationVector(iv) {
            if (this._iv) {
                throw new TypeError("setInitializationVector can only be called once");
            }
            this._iv = iv;
            return this;
        }

        // 启用发行者头部复制
        replicateIssuerAsHeader() {
            this._replicateIssuerAsHeader = true;
            return this;
        }

        // 启用主题头部复制
        replicateSubjectAsHeader() {
            this._replicateSubjectAsHeader = true;
            return this;
        }

        // 启用受众头部复制
        replicateAudienceAsHeader() {
            this._replicateAudienceAsHeader = true;
            return this;
        }

        // 执行JWT加密
        async encrypt(key, options) {
            // 创建紧凑加密实例，载荷为JSON字符串的UTF-8编码
            var compactEncryptor = new compactEncryptClass.CompactEncrypt(
                stringUtilsModule.encoder.encode(JSON.stringify(this._payload))
            );

            // 根据配置复制载荷字段到头部
            if (this._replicateIssuerAsHeader) {
                this._protectedHeader = {
                    ...this._protectedHeader,
                    iss: this._payload.iss
                };
            }

            if (this._replicateSubjectAsHeader) {
                this._protectedHeader = {
                    ...this._protectedHeader,
                    sub: this._payload.sub
                };
            }

            if (this._replicateAudienceAsHeader) {
                this._protectedHeader = {
                    ...this._protectedHeader,
                    aud: this._payload.aud
                };
            }

            // 设置加密参数
            compactEncryptor.setProtectedHeader(this._protectedHeader);

            if (this._iv) {
                compactEncryptor.setInitializationVector(this._iv);
            }

            if (this._cek) {
                compactEncryptor.setContentEncryptionKey(this._cek);
            }

            if (this._keyManagementParameters) {
                compactEncryptor.setKeyManagementParameters(this._keyManagementParameters);
            }

            // 执行加密并返回紧凑格式JWE
            return compactEncryptor.encrypt(key, options);
        }
    };

    exports.EncryptJWT = EncryptJWTClass;
});

// ============================================================================
// JWK (JSON Web Key) 指纹计算模块
// ============================================================================

// JWK 指纹计算模块
var jwkThumbprintModule = moduleWrapper((exports, module) => {  // bke -> jwkThumbprintModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.calculateJwkThumbprint = calculateJwkThumbprint;
    exports.calculateJwkThumbprintUri = calculateJwkThumbprintUri;

    var hashModule = hashGenerator(),
        base64UrlModule = base64UrlEncoder(),
        joseErrorsModule = joseErrors(),
        stringUtilsModule = stringUtils(),
        objectValidatorModule = objectValidator();

    // 参数验证函数
    var validateRequiredParam = (value, paramName) => {
        if ("string" != typeof value || !value) {
            throw new joseErrorsModule.JWKInvalid(paramName + " missing or invalid");
        }
    };

    // 计算JWK指纹
    async function calculateJwkThumbprint(jwk, digestAlgorithm) {
        // 验证JWK是对象
        if (!objectValidatorModule.default(jwk)) {
            throw new TypeError("JWK must be an object");
        }

        // 验证摘要算法
        digestAlgorithm = digestAlgorithm ?? "sha256";
        if ("sha256" !== digestAlgorithm && "sha384" !== digestAlgorithm && "sha512" !== digestAlgorithm) {
            throw new TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');
        }

        let normalizedJwk;

        // 根据密钥类型提取关键参数
        switch (jwk.kty) {
            case "EC":
                // 椭圆曲线密钥
                validateRequiredParam(jwk.crv, '"crv" (Curve) Parameter');
                validateRequiredParam(jwk.x, '"x" (X Coordinate) Parameter');
                validateRequiredParam(jwk.y, '"y" (Y Coordinate) Parameter');
                normalizedJwk = {
                    crv: jwk.crv,
                    kty: jwk.kty,
                    x: jwk.x,
                    y: jwk.y
                };
                break;

            case "OKP":
                // 八进制密钥对
                validateRequiredParam(jwk.crv, '"crv" (Subtype of Key Pair) Parameter');
                validateRequiredParam(jwk.x, '"x" (Public Key) Parameter');
                normalizedJwk = {
                    crv: jwk.crv,
                    kty: jwk.kty,
                    x: jwk.x
                };
                break;

            case "RSA":
                // RSA密钥
                validateRequiredParam(jwk.e, '"e" (Exponent) Parameter');
                validateRequiredParam(jwk.n, '"n" (Modulus) Parameter');
                normalizedJwk = {
                    e: jwk.e,
                    kty: jwk.kty,
                    n: jwk.n
                };
                break;

            case "oct":
                // 对称密钥
                validateRequiredParam(jwk.k, '"k" (Key Value) Parameter');
                normalizedJwk = {
                    k: jwk.k,
                    kty: jwk.kty
                };
                break;

            default:
                throw new joseErrorsModule.JOSENotSupported(
                    '"kty" (Key Type) Parameter missing or unsupported'
                );
        }

        // 计算指纹
        var normalizedJson = stringUtilsModule.encoder.encode(JSON.stringify(normalizedJwk));
        return base64UrlModule.encode(await hashModule.default(digestAlgorithm, normalizedJson));
    }

    // 计算JWK指纹URI
    async function calculateJwkThumbprintUri(jwk, digestAlgorithm) {
        digestAlgorithm = digestAlgorithm ?? "sha256";
        let thumbprint = await calculateJwkThumbprint(jwk, digestAlgorithm);
        return `urn:ietf:params:oauth:jwk-thumbprint:sha-${digestAlgorithm.slice(-3)}:` + thumbprint;
    }
});

// ============================================================================
// JWK 嵌入式密钥处理模块
// ============================================================================

// 嵌入式JWK处理模块
var embeddedJwkModule = moduleWrapper((exports, module) => {  // yke -> embeddedJwkModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.EmbeddedJWK = extractEmbeddedJWK;

    var jwkImporterModule = jwkImporter(),
        objectValidatorModule = objectValidator(),
        joseErrorsModule = joseErrors();

    // 从头部提取嵌入的JWK
    async function extractEmbeddedJWK(protectedHeader, options) {
        // 合并头部信息
        let combinedHeader = {
            ...protectedHeader,
            ...options?.header
        };

        // 验证JWK参数
        if (!objectValidatorModule.default(combinedHeader.jwk)) {
            throw new joseErrorsModule.JWSInvalid(
                '"jwk" (JSON Web Key) Header Parameter must be a JSON object'
            );
        }

        // 导入JWK密钥
        let importedKey = await jwkImporterModule.importJWK({
            ...combinedHeader.jwk,
            ext: true
        }, combinedHeader.alg);

        // 验证密钥类型
        if (importedKey instanceof Uint8Array || "public" !== importedKey.type) {
            throw new joseErrorsModule.JWSInvalid(
                '"jwk" (JSON Web Key) Header Parameter must be a public key'
            );
        }

        return importedKey;
    }
});

// ============================================================================
// 本地JWK集合处理模块
// ============================================================================

// 本地JWK集合模块
var localJwkSetModule = moduleWrapper((exports, module) => {  // Fie -> localJwkSetModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.createLocalJWKSet = createLocalJWKSet;

    var jwkImporterModule = jwkImporter(),
        joseErrorsModule = joseErrors(),
        objectValidatorModule = objectValidator();

    // 验证JWK对象
    function isValidJwk(jwk) {
        return objectValidatorModule.default(jwk);
    }

    // 深拷贝函数
    function deepClone(obj) {
        return "function" == typeof structuredClone ?
            structuredClone(obj) :
            JSON.parse(JSON.stringify(obj));
    }

    // 本地JWK集合类
    var LocalJWKSet = class {
        _jwks;          // JWK集合数据
        _cached = new WeakMap(); // 缓存已导入的密钥

        constructor(jwks) {
            // 验证JWKS格式
            if (!this.isValidJwks(jwks)) {
                throw new joseErrorsModule.JWKSInvalid("JSON Web Key Set malformed");
            }
            this._jwks = deepClone(jwks);
        }

        // 验证JWKS格式
        isValidJwks(jwks) {
            return jwks &&
                   "object" == typeof jwks &&
                   Array.isArray(jwks.keys) &&
                   jwks.keys.every(isValidJwk);
        }

        // 获取匹配的密钥
        async getKey(protectedHeader, options) {
            let {alg: algorithm, kid: keyId} = {
                ...protectedHeader,
                ...options?.header
            };

            // 根据算法确定密钥类型
            let expectedKeyType = this.getKeyTypeFromAlgorithm(algorithm);

            // 过滤匹配的密钥
            let matchingKeys = this._jwks.keys.filter(jwk => {
                let isMatch = expectedKeyType === jwk.kty;

                // 检查密钥ID匹配
                if (isMatch && "string" == typeof keyId) {
                    isMatch = keyId === jwk.kid;
                }

                // 检查算法匹配
                if (isMatch && "string" == typeof jwk.alg) {
                    isMatch = algorithm === jwk.alg;
                }

                // 检查用途匹配
                if (isMatch && "string" == typeof jwk.use) {
                    isMatch = "sig" === jwk.use;
                }

                // 检查密钥操作匹配
                if (isMatch && Array.isArray(jwk.key_ops)) {
                    isMatch = jwk.key_ops.includes("verify");
                }

                // 根据具体算法检查曲线参数
                if (isMatch) {
                    switch (algorithm) {
                        case "ES256":
                            isMatch = "P-256" === jwk.crv;
                            break;
                        case "ES256K":
                            isMatch = "secp256k1" === jwk.crv;
                            break;
                        case "ES384":
                            isMatch = "P-384" === jwk.crv;
                            break;
                        case "ES512":
                            isMatch = "P-521" === jwk.crv;
                            break;
                        case "Ed25519":
                            isMatch = "Ed25519" === jwk.crv;
                            break;
                        case "EdDSA":
                            isMatch = "Ed25519" === jwk.crv || "Ed448" === jwk.crv;
                            break;
                    }
                }

                return isMatch;
            });

            let {0: firstKey, length: matchCount} = matchingKeys;

            // 处理匹配结果
            if (0 === matchCount) {
                throw new joseErrorsModule.JWKSNoMatchingKey();
            }

            if (1 === matchCount) {
                return this.importAndCacheKey(firstKey, algorithm);
            }

            // 多个匹配密钥的情况
            let multipleKeysError = new joseErrorsModule.JWKSMultipleMatchingKeys();
            let cache = this._cached;

            multipleKeysError[Symbol.asyncIterator] = async function*() {
                for (var jwk of matchingKeys) {
                    try {
                        yield await this.importAndCacheKey(cache, jwk, algorithm);
                    } catch {
                        // 忽略导入失败的密钥
                    }
                }
            };

            throw multipleKeysError;
        }

        // 根据算法获取密钥类型
        getKeyTypeFromAlgorithm(algorithm) {
            switch ("string" == typeof algorithm && algorithm.slice(0, 2)) {
                case "RS":
                case "PS":
                    return "RSA";
                case "ES":
                    return "EC";
                case "Ed":
                    return "OKP";
                default:
                    throw new joseErrorsModule.JOSENotSupported(
                        'Unsupported "alg" value for a JSON Web Key Set'
                    );
            }
        }

        // 导入并缓存密钥
        async importAndCacheKey(jwk, algorithm) {
            let keyCache = this._cached.get(jwk) || this._cached.set(jwk, {}).get(jwk);

            if (void 0 === keyCache[algorithm]) {
                let importedKey = await jwkImporterModule.importJWK({
                    ...jwk,
                    ext: true
                }, algorithm);

                if (importedKey instanceof Uint8Array || "public" !== importedKey.type) {
                    throw new joseErrorsModule.JWKSInvalid(
                        "JSON Web Key Set members must be public keys"
                    );
                }

                keyCache[algorithm] = importedKey;
            }

            return keyCache[algorithm];
        }
    };

    // 创建本地JWK集合
    function createLocalJWKSet(jwks) {
        let localSet = new LocalJWKSet(jwks);
        let getKeyFunction = async (protectedHeader, options) => localSet.getKey(protectedHeader, options);

        // 添加额外属性
        Object.defineProperties(getKeyFunction, {
            jwks: {
                value: () => deepClone(localSet._jwks),
                enumerable: true,
                configurable: false,
                writable: false
            }
        });

        return getKeyFunction;
    }
});

// ============================================================================
// HTTP 请求处理模块
// ============================================================================

// HTTP 请求处理模块
var httpRequestModule = moduleWrapper((exports, module) => {  // xke -> httpRequestModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var httpModule = require("http"),
        httpsModule = require("https"),
        eventsModule = require("events"),
        joseErrorsModule = joseErrors(),
        stringUtilsModule = stringUtils();

    // 默认HTTP请求处理函数
    exports.default = async (url, timeout, options) => {
        let requestFunction;

        // 根据协议选择请求函数
        switch (url.protocol) {
            case "https:":
                requestFunction = httpsModule.get;
                break;
            case "http:":
                requestFunction = httpModule.get;
                break;
            default:
                throw new TypeError("Unsupported URL protocol.");
        }

        var {agent: requestAgent, headers: requestHeaders} = options;

        // 发起HTTP请求
        let request = requestFunction(url.href, {
            agent: requestAgent,
            timeout: timeout,
            headers: requestHeaders
        });

        // 等待响应或超时
        let [response] = await Promise.race([
            eventsModule.once(request, "response"),
            eventsModule.once(request, "timeout")
        ]);

        // 处理超时
        if (!response) {
            request.destroy();
            throw new joseErrorsModule.JWKSTimeout();
        }

        // 检查状态码
        if (200 !== response.statusCode) {
            throw new joseErrorsModule.JOSEError(
                "Expected 200 OK from the JSON Web Key Set HTTP response"
            );
        }

        // 收集响应数据
        var chunks = [];
        for await (let chunk of response) {
            chunks.push(chunk);
        }

        // 解析JSON响应
        try {
            return JSON.parse(stringUtilsModule.decoder.decode(
                stringUtilsModule.concat(...chunks)
            ));
        } catch {
            throw new joseErrorsModule.JOSEError(
                "Failed to parse the JSON Web Key Set HTTP response as JSON"
            );
        }
    };
});

// ============================================================================
// 远程JWK集合处理模块
// ============================================================================

// 远程JWK集合模块
var remoteJwkSetModule = moduleWrapper((exports, module) => {  // Tke -> remoteJwkSetModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.experimental_jwksCache = exports.jwksCache = void 0;
    exports.createRemoteJWKSet = createRemoteJWKSet;

    var httpRequestModule = httpRequestModule(),
        joseErrorsModule = joseErrors(),
        localJwkSetModule = localJwkSetModule(),
        objectValidatorModule = objectValidator();

    // 用户代理字符串
    var userAgent;
    if ("undefined" < typeof navigator || !navigator.userAgent?.startsWith?.("Mozilla/5.0 ")) {
        userAgent = "jose/v5.10.0";
    }

    // JWKS缓存符号
    exports.jwksCache = Symbol();

    // 远程JWK集合类
    var RemoteJWKSet = class {
        _url;                   // JWKS URL
        _timeoutDuration;       // 请求超时时间
        _cooldownDuration;      // 冷却时间
        _cacheMaxAge;          // 缓存最大年龄
        _jwksTimestamp;        // JWKS时间戳
        _pendingFetch;         // 待处理的获取请求
        _options;              // 请求选项
        _local;                // 本地JWK集合实例
        _cache;                // 缓存对象

        constructor(url, options) {
            if (!(url instanceof URL)) {
                throw new TypeError("url must be an instance of URL");
            }

            this._url = new URL(url.href);
            this._options = {
                agent: options?.agent,
                headers: options?.headers
            };

            // 设置超时和缓存参数
            this._timeoutDuration = "number" == typeof options?.timeoutDuration ?
                options?.timeoutDuration : 5000;
            this._cooldownDuration = "number" == typeof options?.cooldownDuration ?
                options?.cooldownDuration : 30000;
            this._cacheMaxAge = "number" == typeof options?.cacheMaxAge ?
                options?.cacheMaxAge : 600000;

            // 处理缓存选项
            if (void 0 !== options?.[exports.jwksCache]) {
                this._cache = options?.[exports.jwksCache];
                let cacheData = options?.[exports.jwksCache];
                let maxAge = this._cacheMaxAge;

                // 验证缓存数据有效性
                if ("object" == typeof cacheData &&
                    null !== cacheData &&
                    "uat" in cacheData &&
                    "number" == typeof cacheData.uat &&
                    !(Date.now() - cacheData.uat >= maxAge) &&
                    "jwks" in cacheData &&
                    objectValidatorModule.default(cacheData.jwks) &&
                    Array.isArray(cacheData.jwks.keys) &&
                    Array.prototype.every.call(cacheData.jwks.keys, objectValidatorModule.default)) {

                    this._jwksTimestamp = this._cache.uat;
                    this._local = localJwkSetModule.createLocalJWKSet(this._cache.jwks);
                }
            }
        }

        // 检查是否在冷却期
        coolingDown() {
            return "number" == typeof this._jwksTimestamp &&
                   Date.now() < this._jwksTimestamp + this._cooldownDuration;
        }

        // 检查缓存是否新鲜
        fresh() {
            return "number" == typeof this._jwksTimestamp &&
                   Date.now() < this._jwksTimestamp + this._cacheMaxAge;
        }

        // 获取密钥
        async getKey(protectedHeader, options) {
            // 如果缓存不新鲜，重新加载
            if (!this._local || !this.fresh()) {
                await this.reload();
            }

            try {
                return await this._local(protectedHeader, options);
            } catch (error) {
                // 如果找不到匹配的密钥且不在冷却期，尝试重新加载
                if (error instanceof joseErrorsModule.JWKSNoMatchingKey &&
                    false === this.coolingDown()) {
                    await this.reload();
                    return this._local(protectedHeader, options);
                }
                throw error;
            }
        }

        // 重新加载JWKS
        async reload() {
            // 在某些环境中清除待处理的请求
            if (this._pendingFetch &&
                (typeof WebSocketPair < "undefined" ||
                 typeof navigator < "undefined" && "Cloudflare-Workers" === navigator.userAgent ||
                 typeof EdgeRuntime < "undefined" && "vercel" === EdgeRuntime)) {
                this._pendingFetch = void 0;
            }

            // 设置请求头
            var headers = new Headers(this._options.headers);
            if (userAgent && !headers.has("User-Agent")) {
                headers.set("User-Agent", userAgent);
                this._options.headers = Object.fromEntries(headers.entries());
            }

            // 发起请求（如果没有待处理的请求）
            this._pendingFetch ||= httpRequestModule.default(
                this._url,
                this._timeoutDuration,
                this._options
            ).then(jwks => {
                // 更新本地集合和缓存
                this._local = localJwkSetModule.createLocalJWKSet(jwks);

                if (this._cache) {
                    this._cache.uat = Date.now();
                    this._cache.jwks = jwks;
                }

                this._jwksTimestamp = Date.now();
                this._pendingFetch = void 0;
            }).catch(error => {
                this._pendingFetch = void 0;
                throw error;
            });

            await this._pendingFetch;
        }
    };

    // 创建远程JWK集合
    function createRemoteJWKSet(url, options) {
        let remoteSet = new RemoteJWKSet(url, options);
        let getKeyFunction = async (protectedHeader, options) => remoteSet.getKey(protectedHeader, options);

        // 添加额外属性和方法
        Object.defineProperties(getKeyFunction, {
            coolingDown: {
                get: () => remoteSet.coolingDown(),
                enumerable: true,
                configurable: false
            },
            fresh: {
                get: () => remoteSet.fresh(),
                enumerable: true,
                configurable: false
            },
            reload: {
                value: () => remoteSet.reload(),
                enumerable: true,
                configurable: false,
                writable: false
            },
            reloading: {
                get: () => !!remoteSet._pendingFetch,
                enumerable: true,
                configurable: false
            },
            jwks: {
                value: () => remoteSet._local?.jwks(),
                enumerable: true,
                configurable: false,
                writable: false
            }
        });

        return getKeyFunction;
    }

    // 导出实验性缓存符号
    exports.experimental_jwksCache = exports.jwksCache;
});

// ============================================================================
// 无安全JWT处理模块
// ============================================================================

// 无安全JWT模块
var unsecuredJwtModule = moduleWrapper((exports, module) => {  // Rke -> unsecuredJwtModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.UnsecuredJWT = void 0;

    var base64UrlModule = base64UrlEncoder(),
        stringUtilsModule = stringUtils(),
        joseErrorsModule = joseErrors(),
        jwtValidatorModule = jwtValidator(),
        jwtProducerClass = jwtProducerModule();

    // 无安全JWT类 - 继承自JWT生产者，用于创建无签名的JWT
    var UnsecuredJWTClass = class extends jwtProducerClass.ProduceJWT {
        // 编码为无安全JWT
        encode() {
            return base64UrlModule.encode(JSON.stringify({
                alg: "none"
            })) + `.${base64UrlModule.encode(JSON.stringify(this._payload))}.`;
        }

        // 解码无安全JWT
        static decode(jwt, options) {
            if ("string" != typeof jwt) {
                throw new joseErrorsModule.JWTInvalid("Unsecured JWT must be a string");
            }

            var {0: headerPart, 1: payloadPart, 2: signaturePart, length: partCount} = jwt.split(".");

            if (3 !== partCount || "" !== signaturePart) {
                throw new joseErrorsModule.JWTInvalid("Invalid Unsecured JWT");
            }

            let header;
            try {
                header = JSON.parse(stringUtilsModule.decoder.decode(base64UrlModule.decode(headerPart)));
                if ("none" !== header.alg) {
                    throw new Error();
                }
            } catch {
                throw new joseErrorsModule.JWTInvalid("Invalid Unsecured JWT");
            }

            return {
                payload: jwtValidatorModule.default(header, base64UrlModule.decode(payloadPart), options),
                header: header
            };
        }
    };

    exports.UnsecuredJWT = UnsecuredJWTClass;
});

// ============================================================================
// Base64URL 编码模块导出
// ============================================================================

// Base64URL编码模块导出
var base64UrlExportModule = moduleWrapper((exports, module) => {  // vM -> base64UrlExportModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.decode = exports.encode = void 0;

    var base64UrlModule = base64UrlEncoder();
    exports.encode = base64UrlModule.encode;
    exports.decode = base64UrlModule.decode;
});

// ============================================================================
// 受保护头部解码模块
// ============================================================================

// 受保护头部解码模块
var protectedHeaderDecoderModule = moduleWrapper((exports, module) => {  // Lke -> protectedHeaderDecoderModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.decodeProtectedHeader = decodeProtectedHeader;

    var base64UrlExportModule = base64UrlExportModule(),
        stringUtilsModule = stringUtils(),
        objectValidatorModule = objectValidator();

    // 解码受保护的头部
    function decodeProtectedHeader(token) {
        let protectedHeaderPart;

        if ("string" == typeof token) {
            // 处理字符串格式的token
            var parts = token.split(".");
            if (3 !== parts.length && 5 !== parts.length) {
                // 无效的token格式
            } else {
                [protectedHeaderPart] = parts;
            }
        } else if ("object" == typeof token && token) {
            // 处理对象格式的token
            if (!("protected" in token)) {
                throw new TypeError("Token does not contain a Protected Header");
            }
            protectedHeaderPart = token.protected;
        }

        try {
            if ("string" == typeof protectedHeaderPart && protectedHeaderPart) {
                var decodedHeader = JSON.parse(
                    stringUtilsModule.decoder.decode(
                        base64UrlExportModule.decode(protectedHeaderPart)
                    )
                );

                if (objectValidatorModule.default(decodedHeader)) {
                    return decodedHeader;
                }
            }
            throw new Error();
        } catch {
            throw new TypeError("Invalid Token or Protected Header formatting");
        }
    }
});

// ============================================================================
// JWT 解码模块
// ============================================================================

// JWT解码模块
var jwtDecoderModule = moduleWrapper((exports, module) => {  // Oke -> jwtDecoderModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.decodeJwt = decodeJwt;

    var base64UrlExportModule = base64UrlExportModule(),
        stringUtilsModule = stringUtils(),
        objectValidatorModule = objectValidator(),
        joseErrorsModule = joseErrors();

    // 解码JWT载荷
    function decodeJwt(jwt) {
        if ("string" != typeof jwt) {
            throw new joseErrorsModule.JWTInvalid(
                "JWTs must use Compact JWS serialization, JWT must be a string"
            );
        }

        var {1: payloadPart, length: partCount} = jwt.split(".");

        if (5 === partCount) {
            throw new joseErrorsModule.JWTInvalid(
                "Only JWTs using Compact JWS serialization can be decoded"
            );
        }

        if (3 !== partCount) {
            throw new joseErrorsModule.JWTInvalid("Invalid JWT");
        }

        if (!payloadPart) {
            throw new joseErrorsModule.JWTInvalid("JWTs must contain a payload");
        }

        let decodedPayload;
        try {
            decodedPayload = base64UrlExportModule.decode(payloadPart);
        } catch {
            throw new joseErrorsModule.JWTInvalid("Failed to base64url decode the payload");
        }

        let parsedPayload;
        try {
            parsedPayload = JSON.parse(stringUtilsModule.decoder.decode(decodedPayload));
        } catch {
            throw new joseErrorsModule.JWTInvalid("Failed to parse the decoded payload as JSON");
        }

        if (objectValidatorModule.default(parsedPayload)) {
            return parsedPayload;
        }

        throw new joseErrorsModule.JWTInvalid("Invalid JWT Claims Set");
    }
});

// ============================================================================
// 密钥生成模块
// ============================================================================

// 密钥生成核心模块
var keyGeneratorModule = moduleWrapper((exports, module) => {  // Hie -> keyGeneratorModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.generateSecret = generateSecret;
    exports.generateKeyPair = generateKeyPair;

    var cryptoModule = require("crypto"),
        utilModule = require("util"),
        randomBytesModule = randomBytesGenerator(),
        joseErrorsModule = joseErrors(),
        generateKeyPairAsync = utilModule.promisify(cryptoModule.generateKeyPair);

    // 生成密钥
    async function generateSecret(algorithm, options) {
        let keyLength;

        // 根据算法确定密钥长度
        switch (algorithm) {
            case "HS256":
            case "HS384":
            case "HS512":
            case "A128CBC-HS256":
            case "A192CBC-HS384":
            case "A256CBC-HS512":
                keyLength = parseInt(algorithm.slice(-3), 10);
                break;
            case "A128KW":
            case "A192KW":
            case "A256KW":
            case "A128GCMKW":
            case "A192GCMKW":
            case "A256GCMKW":
            case "A128GCM":
            case "A192GCM":
            case "A256GCM":
                keyLength = parseInt(algorithm.slice(1, 4), 10);
                break;
            default:
                throw new joseErrorsModule.JOSENotSupported(
                    'Invalid or unsupported JWK "alg" (Algorithm) Parameter value'
                );
        }

        return cryptoModule.createSecretKey(
            randomBytesModule.default(new Uint8Array(keyLength >> 3))
        );
    }

    // 生成密钥对
    async function generateKeyPair(algorithm, options) {
        switch (algorithm) {
            case "RS256":
            case "RS384":
            case "RS512":
            case "PS256":
            case "PS384":
            case "PS512":
            case "RSA-OAEP":
            case "RSA-OAEP-256":
            case "RSA-OAEP-384":
            case "RSA-OAEP-512":
            case "RSA1_5":
                var modulusLength = options?.modulusLength ?? 2048;
                if ("number" != typeof modulusLength || modulusLength < 2048) {
                    throw new joseErrorsModule.JOSENotSupported(
                        "Invalid or unsupported modulusLength option provided, 2048 bits or larger keys must be used"
                    );
                }
                return generateKeyPairAsync("rsa", {
                    modulusLength: modulusLength,
                    publicExponent: 65537
                });

            case "ES256":
                return generateKeyPairAsync("ec", {
                    namedCurve: "P-256"
                });

            case "ES256K":
                return generateKeyPairAsync("ec", {
                    namedCurve: "secp256k1"
                });

            case "ES384":
                return generateKeyPairAsync("ec", {
                    namedCurve: "P-384"
                });

            case "ES512":
                return generateKeyPairAsync("ec", {
                    namedCurve: "P-521"
                });

            case "Ed25519":
                return generateKeyPairAsync("ed25519");

            case "EdDSA":
                switch (options?.crv) {
                    case void 0:
                    case "Ed25519":
                        return generateKeyPairAsync("ed25519");
                    case "Ed448":
                        return generateKeyPairAsync("ed448");
                    default:
                        throw new joseErrorsModule.JOSENotSupported(
                            "Invalid or unsupported crv option provided, supported values are Ed25519 and Ed448"
                        );
                }

            case "ECDH-ES":
            case "ECDH-ES+A128KW":
            case "ECDH-ES+A192KW":
            case "ECDH-ES+A256KW":
                var curve = options?.crv ?? "P-256";
                switch (curve) {
                    case void 0:
                    case "P-256":
                    case "P-384":
                    case "P-521":
                        return generateKeyPairAsync("ec", {
                            namedCurve: curve
                        });
                    case "X25519":
                        return generateKeyPairAsync("x25519");
                    case "X448":
                        return generateKeyPairAsync("x448");
                    default:
                        throw new joseErrorsModule.JOSENotSupported(
                            "Invalid or unsupported crv option provided, supported values are P-256, P-384, P-521, X25519, and X448"
                        );
                }

            default:
                throw new joseErrorsModule.JOSENotSupported(
                    'Invalid or unsupported JWK "alg" (Algorithm) Parameter value'
                );
        }
    }
});

// 密钥对生成包装模块
var keyPairGeneratorModule = moduleWrapper((exports, module) => {  // Bke -> keyPairGeneratorModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.generateKeyPair = async function(algorithm, options) {
        return keyGeneratorModule().generateKeyPair(algorithm, options);
    };

    var keyGeneratorModule = keyGeneratorModule();
});

// 密钥生成包装模块
var secretGeneratorModule = moduleWrapper((exports, module) => {  // zke -> secretGeneratorModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.generateSecret = async function(algorithm, options) {
        return keyGeneratorModule().generateSecret(algorithm, options);
    };

    var keyGeneratorModule = keyGeneratorModule();
});

// 加密运行时标识模块
var cryptoRuntimeModule = moduleWrapper((exports, module) => {  // Mke -> cryptoRuntimeModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.default = "node:crypto";
});

// 加密运行时导出模块
var cryptoRuntimeExportModule = moduleWrapper((exports, module) => {  // Fke -> cryptoRuntimeExportModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var cryptoRuntimeModule = cryptoRuntimeModule();
    exports.default = cryptoRuntimeModule.default;
});

// ============================================================================
// JOSE 主导出模块
// ============================================================================

// JOSE 主导出模块 - 统一导出所有JOSE功能
var joseMainExportModule = moduleWrapper((exports, module) => {  // Yke -> joseMainExportModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    // 导出所有JOSE相关功能
    exports.cryptoRuntime = exports.base64url = exports.generateSecret = exports.generateKeyPair =
    exports.errors = exports.decodeJwt = exports.decodeProtectedHeader = exports.importJWK =
    exports.importX509 = exports.importPKCS8 = exports.importSPKI = exports.exportJWK =
    exports.exportSPKI = exports.exportPKCS8 = exports.UnsecuredJWT = exports.experimental_jwksCache =
    exports.jwksCache = exports.createRemoteJWKSet = exports.createLocalJWKSet = exports.EmbeddedJWK =
    exports.calculateJwkThumbprintUri = exports.calculateJwkThumbprint = exports.EncryptJWT =
    exports.SignJWT = exports.GeneralSign = exports.FlattenedSign = exports.CompactSign =
    exports.FlattenedEncrypt = exports.CompactEncrypt = exports.jwtDecrypt = exports.jwtVerify =
    exports.generalVerify = exports.flattenedVerify = exports.compactVerify = exports.GeneralEncrypt =
    exports.generalDecrypt = exports.flattenedDecrypt = exports.compactDecrypt = void 0;

    // 导入各个模块并设置属性描述符
    var compactDecryptModule = compactDecryptModule();
    Object.defineProperty(exports, "compactDecrypt", {
        enumerable: true,
        get: function() {
            return compactDecryptModule.compactDecrypt;
        }
    });

    var flattenedDecryptModule = flattenedDecryptModule();
    Object.defineProperty(exports, "flattenedDecrypt", {
        enumerable: true,
        get: function() {
            return flattenedDecryptModule.flattenedDecrypt;
        }
    });

    var generalDecryptModule = generalDecryptModule();
    Object.defineProperty(exports, "generalDecrypt", {
        enumerable: true,
        get: function() {
            return generalDecryptModule.generalDecrypt;
        }
    });

    var generalEncryptModule = generalEncryptModule();
    Object.defineProperty(exports, "GeneralEncrypt", {
        enumerable: true,
        get: function() {
            return generalEncryptModule.GeneralEncrypt;
        }
    });

    var compactVerifyModule = compactVerifyModule();
    Object.defineProperty(exports, "compactVerify", {
        enumerable: true,
        get: function() {
            return compactVerifyModule.compactVerify;
        }
    });

    var flattenedVerifyModule = flattenedVerifyModule();
    Object.defineProperty(exports, "flattenedVerify", {
        enumerable: true,
        get: function() {
            return flattenedVerifyModule.flattenedVerify;
        }
    });

    var generalVerifyModule = generalVerifyModule();
    Object.defineProperty(exports, "generalVerify", {
        enumerable: true,
        get: function() {
            return generalVerifyModule.generalVerify;
        }
    });

    var jwtVerifyModule = jwtVerifyModule();
    Object.defineProperty(exports, "jwtVerify", {
        enumerable: true,
        get: function() {
            return jwtVerifyModule.jwtVerify;
        }
    });

    var jwtDecryptModule = jwtDecryptModule();
    Object.defineProperty(exports, "jwtDecrypt", {
        enumerable: true,
        get: function() {
            return jwtDecryptModule.jwtDecrypt;
        }
    });

    var compactEncryptModule = compactEncryptModule();
    Object.defineProperty(exports, "CompactEncrypt", {
        enumerable: true,
        get: function() {
            return compactEncryptModule.CompactEncrypt;
        }
    });

    var flattenedEncryptModule = flattenedEncryptModule();
    Object.defineProperty(exports, "FlattenedEncrypt", {
        enumerable: true,
        get: function() {
            return flattenedEncryptModule.FlattenedEncrypt;
        }
    });

    var compactSignModule = compactSignModule();
    Object.defineProperty(exports, "CompactSign", {
        enumerable: true,
        get: function() {
            return compactSignModule.CompactSign;
        }
    });

    var flattenedSignModule = flattenedSignModule();
    Object.defineProperty(exports, "FlattenedSign", {
        enumerable: true,
        get: function() {
            return flattenedSignModule.FlattenedSign;
        }
    });

    var generalSignModule = generalSignModule();
    Object.defineProperty(exports, "GeneralSign", {
        enumerable: true,
        get: function() {
            return generalSignModule.GeneralSign;
        }
    });

    var signJwtModule = signJwtModule();
    Object.defineProperty(exports, "SignJWT", {
        enumerable: true,
        get: function() {
            return signJwtModule.SignJWT;
        }
    });

    var encryptJwtModule = encryptJwtModule();
    Object.defineProperty(exports, "EncryptJWT", {
        enumerable: true,
        get: function() {
            return encryptJwtModule.EncryptJWT;
        }
    });

    var jwkThumbprintModule = jwkThumbprintModule();
    Object.defineProperty(exports, "calculateJwkThumbprint", {
        enumerable: true,
        get: function() {
            return jwkThumbprintModule.calculateJwkThumbprint;
        }
    });
    Object.defineProperty(exports, "calculateJwkThumbprintUri", {
        enumerable: true,
        get: function() {
            return jwkThumbprintModule.calculateJwkThumbprintUri;
        }
    });

    var embeddedJwkModule = embeddedJwkModule();
    Object.defineProperty(exports, "EmbeddedJWK", {
        enumerable: true,
        get: function() {
            return embeddedJwkModule.EmbeddedJWK;
        }
    });

    var localJwkSetModule = localJwkSetModule();
    Object.defineProperty(exports, "createLocalJWKSet", {
        enumerable: true,
        get: function() {
            return localJwkSetModule.createLocalJWKSet;
        }
    });

    var remoteJwkSetModule = remoteJwkSetModule();
    Object.defineProperty(exports, "createRemoteJWKSet", {
        enumerable: true,
        get: function() {
            return remoteJwkSetModule.createRemoteJWKSet;
        }
    });
    Object.defineProperty(exports, "jwksCache", {
        enumerable: true,
        get: function() {
            return remoteJwkSetModule.jwksCache;
        }
    });
    Object.defineProperty(exports, "experimental_jwksCache", {
        enumerable: true,
        get: function() {
            return remoteJwkSetModule.experimental_jwksCache;
        }
    });

    var unsecuredJwtModule = unsecuredJwtModule();
    Object.defineProperty(exports, "UnsecuredJWT", {
        enumerable: true,
        get: function() {
            return unsecuredJwtModule.UnsecuredJWT;
        }
    });

    // 导出密钥相关功能
    var keyExporterModule = keyExporterModule();
    Object.defineProperty(exports, "exportPKCS8", {
        enumerable: true,
        get: function() {
            return keyExporterModule.exportPKCS8;
        }
    });
    Object.defineProperty(exports, "exportSPKI", {
        enumerable: true,
        get: function() {
            return keyExporterModule.exportSPKI;
        }
    });
    Object.defineProperty(exports, "exportJWK", {
        enumerable: true,
        get: function() {
            return keyExporterModule.exportJWK;
        }
    });

    var keyImporterModule = keyImporterModule();
    Object.defineProperty(exports, "importSPKI", {
        enumerable: true,
        get: function() {
            return keyImporterModule.importSPKI;
        }
    });
    Object.defineProperty(exports, "importPKCS8", {
        enumerable: true,
        get: function() {
            return keyImporterModule.importPKCS8;
        }
    });
    Object.defineProperty(exports, "importX509", {
        enumerable: true,
        get: function() {
            return keyImporterModule.importX509;
        }
    });
    Object.defineProperty(exports, "importJWK", {
        enumerable: true,
        get: function() {
            return keyImporterModule.importJWK;
        }
    });

    var protectedHeaderDecoderModule = protectedHeaderDecoderModule();
    Object.defineProperty(exports, "decodeProtectedHeader", {
        enumerable: true,
        get: function() {
            return protectedHeaderDecoderModule.decodeProtectedHeader;
        }
    });

    var jwtDecoderModule = jwtDecoderModule();
    Object.defineProperty(exports, "decodeJwt", {
        enumerable: true,
        get: function() {
            return jwtDecoderModule.decodeJwt;
        }
    });

    // 导出错误模块
    exports.errors = joseErrors();

    var keyPairGeneratorModule = keyPairGeneratorModule();
    Object.defineProperty(exports, "generateKeyPair", {
        enumerable: true,
        get: function() {
            return keyPairGeneratorModule.generateKeyPair;
        }
    });

    var secretGeneratorModule = secretGeneratorModule();
    Object.defineProperty(exports, "generateSecret", {
        enumerable: true,
        get: function() {
            return secretGeneratorModule.generateSecret;
        }
    });

    // 导出base64url和加密运行时
    exports.base64url = base64UrlExportModule();

    var cryptoRuntimeExportModule = cryptoRuntimeExportModule();
    Object.defineProperty(exports, "cryptoRuntime", {
        enumerable: true,
        get: function() {
            return cryptoRuntimeExportModule.default;
        }
    });
});

// ============================================================================
// Token 管理模块
// ============================================================================

// Token 管理模块
var tokenManagerModule = moduleWrapper((exports, module) => {  // Qke -> tokenManagerModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.TokenManager = void 0;

    var uuidModule = uuidGenerator(),
        joseModule = joseMainExportModule(),
        backoffModule = backoffUtility(),
        emitterModule = emitterUtility();

    // 标准化头部信息
    function normalizeHeaders(headers) {
        var normalizedHeaders = {};
        if (!headers) return {};

        // 检查是否有entries方法
        if ("object" == typeof headers &&
            null !== headers &&
            "entries" in Object(headers) &&
            "function" == typeof Object(headers).entries) {
            // 使用entries方法
            for (var [key, value] of headers.entries()) {
                normalizedHeaders[key.toLowerCase()] = value;
            }
        } else {
            // 使用Object.entries
            for (var [key, value] of Object.entries(headers)) {
                normalizedHeaders[key.toLowerCase()] = value;
            }
        }
        return normalizedHeaders;
    }

    // Token 管理器类
    exports.TokenManager = class {
        alg = "RS256";                          // 签名算法
        grantType = "client_credentials";       // 授权类型
        clientAssertionType = "urn:ietf:params:oauth:client-assertion-type:jwt-bearer"; // 客户端断言类型
        clientId;                               // 客户端ID
        clientKey;                              // 客户端密钥
        keyId;                                  // 密钥ID
        scope;                                  // 权限范围
        authServer;                             // 认证服务器
        httpClient;                             // HTTP客户端
        maxRetries;                             // 最大重试次数
        clockSkewInSeconds = 0;                 // 时钟偏差（秒）
        accessToken;                            // 访问令牌
        tokenEmitter = new emitterModule.Emitter(); // 令牌事件发射器
        retryCount;                             // 重试计数
        pollerTimer;                            // 轮询定时器

        constructor(options) {
            this.keyId = options.keyId;
            this.clientId = options.clientId;
            this.clientKey = options.clientKey;
            this.authServer = options.authServer ?? "https://oauth2.segment.io";
            this.scope = options.scope ?? "tracking_api:write";
            this.httpClient = options.httpClient;
            this.maxRetries = options.maxRetries;

            // 监听访问令牌事件
            this.tokenEmitter.on("access_token", tokenEvent => {
                if ("token" in tokenEvent) {
                    this.accessToken = tokenEvent.token;
                }
            });

            this.retryCount = 0;
        }

        // 停止轮询器
        stopPoller() {
            clearTimeout(this.pollerTimer);
        }

        // 轮询循环
        async pollerLoop() {
            let nextPollDelay = 25;
            let response;

            try {
                response = await this.requestAccessToken();
            } catch (error) {
                return this.handleTransientError({
                    error: error
                });
            }

            // 验证响应是否有text方法
            if ("function" != typeof response.text) {
                return this.handleInvalidCustomResponse();
            }

            var normalizedHeaders = normalizeHeaders(response.headers);

            // 更新时钟偏差
            if (normalizedHeaders.date) {
                this.updateClockSkew(Date.parse(normalizedHeaders.date));
            }

            // 处理非200状态码
            if (200 !== response.status) {
                if (429 === response.status) {
                    // 处理限流
                    return await this.handleRateLimited(response, normalizedHeaders, nextPollDelay);
                } else if ([400, 401, 415].includes(response.status)) {
                    // 处理不可恢复的错误
                    return this.handleUnrecoverableErrors(response);
                } else {
                    // 处理其他临时错误
                    return this.handleTransientError({
                        error: new Error(`[${response.status}] ` + response.statusText)
                    });
                }
            }

            try {
                var responseText = await response.text();
                var tokenData = JSON.parse(responseText);

                // 验证令牌数据格式
                if (this.isValidTokenResponse(tokenData)) {
                    // 设置过期时间
                    tokenData.expires_at = Math.round(Date.now() / 1000) + tokenData.expires_in;

                    // 发射令牌事件
                    this.tokenEmitter.emit("access_token", {
                        token: tokenData
                    });

                    // 重置重试计数
                    this.retryCount = 0;

                    // 计算下次轮询延迟（令牌有效期的一半）
                    nextPollDelay = tokenData.expires_in / 2 * 1000;

                    return this.queueNextPoll(nextPollDelay);
                }

                throw new Error("Response did not contain a valid access_token and expires_in");
            } catch (error) {
                return this.handleTransientError({
                    error: error,
                    forceEmitError: true
                });
            }
        }

        // 验证令牌响应格式
        isValidTokenResponse(tokenData) {
            return tokenData &&
                   "object" == typeof tokenData &&
                   "access_token" in tokenData &&
                   "expires_in" in tokenData &&
                   "string" == typeof tokenData.access_token &&
                   "number" == typeof tokenData.expires_in;
        }

        // 处理临时错误
        handleTransientError({error, forceEmitError}) {
            this.incrementRetries({
                error: error,
                forceEmitError: forceEmitError
            });

            var backoffDelay = backoffModule.backoff({
                attempt: this.retryCount,
                minTimeout: 25,
                maxTimeout: 1000
            });

            this.queueNextPoll(backoffDelay);
        }

        // 处理无效的自定义响应
        handleInvalidCustomResponse() {
            this.tokenEmitter.emit("access_token", {
                error: new Error("HTTPClient does not implement response.text method")
            });
        }

        // 处理限流
        async handleRateLimited(response, headers, defaultDelay) {
            this.incrementRetries({
                error: new Error(`[${response.status}] ` + response.statusText)
            });

            var resetDelay = defaultDelay;

            // 检查限流重置时间
            if (headers["x-ratelimit-reset"]) {
                var resetTime = parseInt(headers["x-ratelimit-reset"], 10);
                if (isFinite(resetTime)) {
                    resetDelay = resetTime - Date.now() + 1000 * this.clockSkewInSeconds;
                } else {
                    resetDelay = 5000; // 默认5秒
                }
                await backoffModule.sleep(resetDelay);
                resetDelay = 0;
            }

            this.queueNextPoll(resetDelay);
        }

        // 处理不可恢复的错误
        handleUnrecoverableErrors(response) {
            this.retryCount = 0;
            this.tokenEmitter.emit("access_token", {
                error: new Error(`[${response.status}] ` + response.statusText)
            });
            this.stopPoller();
        }

        // 更新时钟偏差
        updateClockSkew(serverTime) {
            this.clockSkewInSeconds = (Date.now() - serverTime) / 1000;
        }

        // 增加重试次数
        incrementRetries({error, forceEmitError}) {
            this.retryCount++;

            // 如果强制发射错误或达到最大重试次数，发射错误事件
            if (forceEmitError || this.retryCount % this.maxRetries === 0) {
                this.retryCount = 0;
                this.tokenEmitter.emit("access_token", {
                    error: error
                });
            }
        }

        // 队列下次轮询
        queueNextPoll(delay) {
            this.pollerTimer = setTimeout(() => this.pollerLoop(), delay);
            // 在Node.js中取消引用定时器，避免阻止进程退出
            if (this.pollerTimer.unref) {
                this.pollerTimer.unref();
            }
        }

        // 请求访问令牌
        async requestAccessToken() {
            var jwtId = uuidModule.uuid();
            var currentTime = Math.round(Date.now() / 1000) - this.clockSkewInSeconds;

            // 构建JWT载荷
            var jwtPayload = {
                iss: this.clientId,     // 发行者
                sub: this.clientId,     // 主题
                aud: this.authServer,   // 受众
                iat: currentTime - 5,   // 签发时间（提前5秒）
                exp: 55 + currentTime,  // 过期时间（55秒后）
                jti: jwtId              // JWT ID
            };

            // 导入私钥并签名JWT
            var privateKey = await joseModule.importPKCS8(this.clientKey, "RS256");
            var signedJwt = await new joseModule.SignJWT(jwtPayload)
                .setProtectedHeader({
                    alg: this.alg,
                    kid: this.keyId,
                    typ: "JWT"
                })
                .sign(privateKey);

            // 构建请求体
            var requestBody = `grant_type=${this.grantType}&client_assertion_type=${this.clientAssertionType}&client_assertion=${signedJwt}&scope=` + this.scope;

            // 构建请求配置
            var requestConfig = {
                method: "POST",
                url: this.authServer + "/token",
                body: requestBody,
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded"
                },
                httpRequestTimeout: 10000
            };

            return this.httpClient.makeRequest(requestConfig);
        }

        // 获取访问令牌
        async getAccessToken() {
            if (this.isValidToken(this.accessToken)) {
                return this.accessToken;
            }

            // 停止当前轮询并启动新的轮询
            this.stopPoller();
            this.pollerLoop().catch(() => {});

            // 返回Promise等待令牌
            return new Promise((resolve, reject) => {
                this.tokenEmitter.once("access_token", tokenEvent => {
                    if ("token" in tokenEvent) {
                        resolve(tokenEvent.token);
                    } else {
                        reject(tokenEvent.error);
                    }
                });
            });
        }

        // 清除令牌
        clearToken() {
            this.accessToken = void 0;
        }

        // 验证令牌是否有效
        isValidToken(token) {
            return typeof token !== "undefined" &&
                   null !== token &&
                   token.expires_in < Date.now() / 1000;
        }
    };
});

// ============================================================================
// 发布者模块
// ============================================================================

// 发布者模块
var publisherModule = moduleWrapper((exports, module) => {  // Uke -> publisherModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.Publisher = void 0;

    var backoffModule = backoffUtility(),
        urlUtilsModule = urlUtils(),
        emitterModule = emitterUtility(),
        contextBatchModule = contextBatch(),
        tokenManagerModule = tokenManagerModule();

    // 空函数
    function noop() {}

    // 处理批次失败
    function handleBatchFailure(batch, reason) {
        batch.getContexts().forEach(context => {
            context.setFailedDelivery({
                reason: reason
            });
        });
        batch.resolveEvents();
    }

    // 发布者类
    exports.Publisher = class {
        pendingFlushTimeout;        // 待处理的刷新超时
        _batch;                     // 当前批次
        _flushInterval;             // 刷新间隔
        _flushAt;                   // 刷新阈值
        _maxRetries;                // 最大重试次数
        _url;                       // 目标URL
        _flushPendingItemsCount;    // 待刷新项目计数
        _httpRequestTimeout;        // HTTP请求超时
        _emitter;                   // 事件发射器
        _disable;                   // 是否禁用
        _httpClient;                // HTTP客户端
        _writeKey;                  // 写入密钥
        _tokenManager;              // 令牌管理器

        constructor({
            host,
            path,
            maxRetries,
            flushAt,
            flushInterval,
            writeKey,
            httpRequestTimeout,
            httpClient,
            disable,
            oauthSettings
        }, emitter) {
            this._emitter = emitter;
            this._maxRetries = maxRetries;
            this._flushAt = Math.max(flushAt, 1);
            this._flushInterval = flushInterval;
            this._url = urlUtilsModule.tryCreateFormattedUrl(
                host ?? "https://api.segment.io",
                path ?? "/v1/batch"
            );
            this._httpRequestTimeout = httpRequestTimeout ?? 10000;
            this._disable = !!disable;
            this._httpClient = httpClient;
            this._writeKey = writeKey;

            // 设置OAuth令牌管理器
            if (oauthSettings) {
                this._tokenManager = new tokenManagerModule.TokenManager({
                    ...oauthSettings,
                    httpClient: oauthSettings.httpClient ?? httpClient,
                    maxRetries: oauthSettings.maxRetries ?? maxRetries
                });
            }
        }

        // 创建批次
        createBatch() {
            // 清除现有的待处理超时
            if (this.pendingFlushTimeout) {
                clearTimeout(this.pendingFlushTimeout);
            }

            let batch = new contextBatchModule.ContextBatch(this._flushAt);
            this._batch = batch;

            // 设置自动刷新定时器
            this.pendingFlushTimeout = setTimeout(() => {
                if (batch === this._batch) {
                    this._batch = void 0;
                }
                this.pendingFlushTimeout = void 0;

                if (batch.length) {
                    this.send(batch).catch(noop);
                }
            }, this._flushInterval);

            return batch;
        }

        // 清除批次
        clearBatch() {
            if (this.pendingFlushTimeout) {
                clearTimeout(this.pendingFlushTimeout);
            }
            this._batch = void 0;
        }

        // 刷新
        flush(pendingItemsCount) {
            if (pendingItemsCount) {
                this._flushPendingItemsCount = pendingItemsCount;

                if (this._batch && this._batch.length === pendingItemsCount) {
                    this.send(this._batch).catch(noop).finally(() => {
                        if (this._tokenManager) {
                            this._tokenManager.stopPoller();
                        }
                    });
                    this.clearBatch();
                }
            } else if (this._tokenManager) {
                this._tokenManager.stopPoller();
            }
        }

        // 入队
        enqueue(context) {
            var batch = this._batch ?? this.createBatch();
            var {promise, resolve} = emitterModule.createDeferred();
            var batchItem = {
                context: context,
                resolver: resolve
            };

            var addResult = batch.tryAdd(batchItem);
            if (addResult.success) {
                var shouldFlushEarly = batch.length === this._flushPendingItemsCount;

                if (batch.length === this._flushAt || shouldFlushEarly) {
                    this.send(batch).catch(noop);
                    this.clearBatch();
                }

                return promise;
            }

            // 如果当前批次已满，发送它并创建新批次
            if (batch.length) {
                this.send(batch).catch(noop);
                this.clearBatch();
            }

            var newBatch = this.createBatch();
            var newAddResult = newBatch.tryAdd(batchItem);

            if (newAddResult.success) {
                if (newBatch.length === this._flushPendingItemsCount) {
                    this.send(newBatch).catch(noop);
                    this.clearBatch();
                }
                return promise;
            } else {
                // 添加失败，设置失败交付
                context.setFailedDelivery({
                    reason: new Error(newAddResult.message)
                });
                return Promise.resolve(context);
            }
        }

        // 发送批次
        async send(batch) {
            if (this._flushPendingItemsCount) {
                this._flushPendingItemsCount -= batch.length;
            }

            let events = batch.getEvents();
            let maxAttempts = this._maxRetries + 1;
            let attempt = 0;

            while (attempt < maxAttempts) {
                attempt++;
                let rateLimitDelay;
                let error;

                try {
                    // 如果禁用，直接解析事件
                    if (this._disable) {
                        return batch.resolveEvents();
                    }

                    let authorizationHeader;

                    // 获取授权头
                    if (this._tokenManager) {
                        let accessToken = await this._tokenManager.getAccessToken();
                        if (accessToken && accessToken.access_token) {
                            authorizationHeader = "Bearer " + accessToken.access_token;
                        }
                    }

                    // 构建请求头
                    var headers = {
                        "Content-Type": "application/json",
                        "User-Agent": "analytics-node-next/latest",
                        ...(authorizationHeader ? {
                            Authorization: authorizationHeader
                        } : {})
                    };

                    // 构建请求配置
                    var requestConfig = {
                        url: this._url,
                        method: "POST",
                        headers: headers,
                        body: JSON.stringify({
                            batch: events,
                            writeKey: this._writeKey,
                            sentAt: new Date()
                        }),
                        httpRequestTimeout: this._httpRequestTimeout
                    };

                    // 发射HTTP请求事件
                    this._emitter.emit("http_request", {
                        body: requestConfig.body,
                        method: requestConfig.method,
                        url: requestConfig.url,
                        headers: requestConfig.headers
                    });

                    // 发送请求
                    var response = await this._httpClient.makeRequest(requestConfig);

                    // 处理成功响应
                    if (200 <= response.status && response.status < 300) {
                        return void batch.resolveEvents();
                    }

                    // 处理认证错误
                    if (this._tokenManager &&
                        (400 === response.status || 401 === response.status || 403 === response.status)) {
                        this._tokenManager.clearToken();
                        error = new Error(`[${response.status}] ` + response.statusText);
                    } else if (400 === response.status) {
                        // 400错误不重试
                        return void handleBatchFailure(batch, new Error(`[${response.status}] ` + response.statusText));
                    } else {
                        // 处理限流
                        if (429 === response.status &&
                            response.headers &&
                            "x-ratelimit-reset" in response.headers) {
                            let resetTime = parseInt(response.headers["x-ratelimit-reset"], 10);
                            if (isFinite(resetTime)) {
                                rateLimitDelay = resetTime - Date.now();
                            }
                        }
                        error = new Error(`[${response.status}] ` + response.statusText);
                    }
                } catch (requestError) {
                    error = requestError;
                }

                // 如果是最后一次尝试，处理失败
                if (attempt === maxAttempts) {
                    return void handleBatchFailure(batch, error);
                }

                // 等待重试延迟
                await new Promise(resolve => setTimeout(resolve,
                    rateLimitDelay || backoffModule.backoff({
                        attempt: attempt,
                        minTimeout: 25,
                        maxTimeout: 1000
                    })
                ));
            }
        }
    };
});

// ============================================================================
// 运行时检测模块
// ============================================================================

// 运行时检测模块
var runtimeDetectorModule = moduleWrapper((exports, module) => {  // rae -> runtimeDetectorModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.detectRuntime = void 0;

    // 检测运行时环境
    exports.detectRuntime = () => {
        // 检测Node.js环境
        if ("object" == typeof process &&
            process &&
            "object" == typeof process.env &&
            process.env &&
            "string" == typeof process.version) {
            return "node";
        }

        // 检测浏览器环境
        if ("object" == typeof window) {
            return "browser";
        }

        // 检测Cloudflare Worker环境
        if (typeof WebSocketPair !== "undefined") {
            return "cloudflare-worker";
        }

        // 检测Vercel Edge环境
        if ("string" == typeof EdgeRuntime) {
            return "vercel-edge";
        }

        // 检测Web Worker环境
        if (typeof WorkerGlobalScope !== "undefined" &&
            "function" == typeof importScripts) {
            return "web-worker";
        }

        return "unknown";
    };
});

// ============================================================================
// Node.js 插件模块
// ============================================================================

// Node.js 插件模块
var nodePluginModule = moduleWrapper((exports, module) => {  // Hke -> nodePluginModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.createConfiguredNodePlugin = exports.createNodePlugin = void 0;

    var publisherModule = publisherModule(),
        packageInfoModule = packageInfo(),
        runtimeDetectorModule = runtimeDetectorModule();

    // 创建Node.js插件
    function createNodePlugin(publisher) {
        function processEvent(event) {
            // 更新事件的库信息
            event = event.updateEvent("context.library.name", "@segment/analytics-node");
            event = event.updateEvent("context.library.version", packageInfoModule.version);

            // 检测运行时并添加元数据
            var runtime = runtimeDetectorModule.detectRuntime();
            if ("node" === runtime) {
                event = event.updateEvent("_metadata.nodeVersion", process.version);
            }
            event = event.updateEvent("_metadata.jsRuntime", runtime);

            return publisher.enqueue(event);
        }

        return {
            name: "Segment.io",
            type: "destination",
            version: "1.0.0",
            isLoaded: () => true,
            load: () => Promise.resolve(),
            alias: processEvent,
            group: processEvent,
            identify: processEvent,
            page: processEvent,
            screen: processEvent,
            track: processEvent
        };
    }

    exports.createNodePlugin = createNodePlugin;

    // 创建配置的Node.js插件
    exports.createConfiguredNodePlugin = (publisherConfig, emitter) => {
        var publisher = new publisherModule.Publisher(publisherConfig, emitter);
        return {
            publisher: publisher,
            plugin: createNodePlugin(publisher)
        };
    };
});

// ============================================================================
// 消息ID生成模块
// ============================================================================

// 消息ID生成模块
var messageIdGeneratorModule = moduleWrapper((exports, module) => {  // Gke -> messageIdGeneratorModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.createMessageId = void 0;

    var uuidModule = uuidGenerator();

    // 创建消息ID
    exports.createMessageId = () => `node-next-${Date.now()}-` + uuidModule.uuid();
});

// ============================================================================
// Node.js 事件工厂模块
// ============================================================================

// Node.js 事件工厂模块
var nodeEventFactoryModule = moduleWrapper((exports, module) => {  // Kke -> nodeEventFactoryModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.NodeEventFactory = void 0;

    var coreAnalyticsModule = coreAnalytics(),
        messageIdGeneratorModule = messageIdGeneratorModule();

    // Node.js 事件工厂类
    var NodeEventFactory = class extends coreAnalyticsModule.CoreEventFactory {
        constructor() {
            super({
                createMessageId: messageIdGeneratorModule.createMessageId,
                onFinishedEvent: event => {
                    coreAnalyticsModule.assertUserIdentity(event);
                }
            });
        }
    };

    exports.NodeEventFactory = NodeEventFactory;
});

// ============================================================================
// Node.js 上下文模块
// ============================================================================

// Node.js 上下文模块
var nodeContextModule = moduleWrapper((exports, module) => {  // TM -> nodeContextModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.Context = void 0;

    var coreAnalyticsModule = coreAnalytics();

    // Node.js 上下文类
    var Context = class extends coreAnalyticsModule.CoreContext {
        // 创建系统上下文
        static system() {
            return new this({
                type: "track",
                event: "system"
            });
        }
    };

    exports.Context = Context;
});

// ============================================================================
// 事件分发和发射模块
// ============================================================================

// 事件分发和发射模块
var dispatchEmitModule = moduleWrapper((exports, module) => {  // Jke -> dispatchEmitModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.dispatchAndEmit = void 0;

    var coreAnalyticsModule = coreAnalytics(),
        nodeContextModule = nodeContextModule();

    // 分发和发射事件
    exports.dispatchAndEmit = async (eventData, plugins, emitter, callback) => {
        try {
            // 创建上下文
            var context = new nodeContextModule.Context(eventData);

            // 分发事件
            var processedContext = await coreAnalyticsModule.dispatch(context, plugins, emitter, {
                ...(callback ? {
                    callback: (originalCallback => event => {
                        var failedDelivery = event.failedDelivery();
                        return originalCallback(failedDelivery ? failedDelivery.reason : void 0, event);
                    })(callback)
                } : {})
            });

            // 检查是否有失败的交付
            var failedDelivery = processedContext.failedDelivery();
            if (failedDelivery) {
                emitter.emit("error", {
                    code: "delivery_failure",
                    reason: failedDelivery.reason,
                    ctx: processedContext
                });
            } else {
                emitter.emit(eventData.type, processedContext);
            }
        } catch (error) {
            emitter.emit("error", {
                code: "unknown",
                reason: error
            });
        }
    };
});

// ============================================================================
// Node.js 事件发射器模块
// ============================================================================

// Node.js 事件发射器模块
var nodeEmitterModule = moduleWrapper((exports, module) => {  // Xke -> nodeEmitterModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.NodeEmitter = void 0;

    var emitterModule = emitterUtility();

    // Node.js 事件发射器类
    var NodeEmitter = class extends emitterModule.Emitter {};

    exports.NodeEmitter = NodeEmitter;
});

// ============================================================================
// Node.js 事件队列模块
// ============================================================================

// Node.js 事件队列模块
var nodeEventQueueModule = moduleWrapper((exports, module) => {  // eIe -> nodeEventQueueModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.NodeEventQueue = void 0;

    var coreAnalyticsModule = coreAnalytics();

    // Node.js 优先级队列类
    var NodePriorityQueue = class extends coreAnalyticsModule.PriorityQueue {
        constructor() {
            super(1, []);
        }

        // 获取尝试次数
        getAttempts(item) {
            return item.attempts ?? 0;
        }

        // 更新尝试次数
        updateAttempts(item) {
            item.attempts = this.getAttempts(item) + 1;
            return this.getAttempts(item);
        }
    };

    // Node.js 事件队列类
    var NodeEventQueue = class extends coreAnalyticsModule.CoreEventQueue {
        constructor() {
            super(new NodePriorityQueue());
        }
    };

    exports.NodeEventQueue = NodeEventQueue;
});

// ============================================================================
// 中止控制器模块
// ============================================================================

// 中止控制器模块
var abortControllerModule = moduleWrapper((exports, module) => {  // tIe -> abortControllerModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.abortSignalAfterTimeout = exports.AbortController = exports.AbortSignal = void 0;

    var emitterModule = emitterUtility(),
        runtimeDetectorModule = runtimeDetectorModule();

    // 中止信号类
    var AbortSignal = class {
        onabort = null;                         // 中止回调
        aborted = false;                        // 是否已中止
        eventEmitter = new emitterModule.Emitter; // 事件发射器

        toString() {
            return "[object AbortSignal]";
        }

        get [Symbol.toStringTag]() {
            return "AbortSignal";
        }

        // 移除事件监听器
        removeEventListener(...args) {
            this.eventEmitter.off(...args);
        }

        // 添加事件监听器
        addEventListener(...args) {
            this.eventEmitter.on(...args);
        }

        // 分发事件
        dispatchEvent(eventType) {
            var event = {
                type: eventType,
                target: this
            };
            var handlerName = "on" + eventType;

            if ("function" == typeof this[handlerName]) {
                this[handlerName](event);
            }

            this.eventEmitter.emit(eventType, event);
        }
    };

    exports.AbortSignal = AbortSignal;

    // 中止控制器类
    var AbortController = class {
        signal = new AbortSignal();

        // 中止操作
        abort() {
            if (!this.signal.aborted) {
                this.signal.aborted = true;
                this.signal.dispatchEvent("abort");
            }
        }

        toString() {
            return "[object AbortController]";
        }

        get [Symbol.toStringTag]() {
            return "AbortController";
        }
    };

    exports.AbortController = AbortController;

    // 超时后中止信号
    exports.abortSignalAfterTimeout = timeout => {
        // Cloudflare Worker环境不支持setTimeout
        if ("cloudflare-worker" === runtimeDetectorModule.detectRuntime()) {
            return [];
        }

        let controller = new (globalThis.AbortController || AbortController)();
        let timeoutId = setTimeout(() => {
            controller.abort();
        }, timeout);

        // 在Node.js中取消引用定时器
        if (timeoutId?.unref) {
            timeoutId.unref();
        }

        return [controller.signal, timeoutId];
    };
});

// ============================================================================
// WebIDL 类型转换模块
// ============================================================================

// WebIDL 类型转换模块
var webIdlTypesModule = moduleWrapper((exports, module) => {  // sIe -> webIdlTypesModule
    var typeConverters = {};

    // 获取数字符号
    function getSign(number) {
        return number < 0 ? -1 : 1;
    }

    // 创建整数类型转换器
    function createIntegerConverter(bitLength, options) {
        options.unsigned || --bitLength;

        let minValue = options.unsigned ? 0 : -Math.pow(2, bitLength);
        let maxValue = Math.pow(2, bitLength) - 1;
        let moduloValue = options.moduloBitLength ? Math.pow(2, options.moduloBitLength) : Math.pow(2, bitLength);
        let halfModulo = options.moduloBitLength ? Math.pow(2, options.moduloBitLength - 1) : Math.pow(2, bitLength - 1);

        return function(value, conversionOptions) {
            let numericValue = +value;
            conversionOptions = conversionOptions || {};

            if (conversionOptions.enforceRange) {
                if (!Number.isFinite(numericValue)) {
                    throw new TypeError("Argument is not a finite number");
                }

                numericValue = getSign(numericValue) * Math.floor(Math.abs(numericValue));

                if (numericValue < minValue || numericValue > maxValue) {
                    throw new TypeError("Argument is not in byte range");
                }
            } else if (!isNaN(numericValue) && conversionOptions.clamp) {
                // 使用银行家舍入法
                numericValue = numericValue % 1 !== 0.5 || numericValue & 1 ?
                    Math.round(numericValue) : Math.floor(numericValue);

                numericValue = numericValue < minValue ? minValue :
                    numericValue > maxValue ? maxValue : numericValue;
            } else {
                if (!Number.isFinite(numericValue) || 0 === numericValue) {
                    return 0;
                }

                numericValue = getSign(numericValue) * Math.floor(Math.abs(numericValue));
                numericValue %= moduloValue;

                if (!options.unsigned && numericValue >= halfModulo) {
                    return numericValue - moduloValue;
                }

                if (options.unsigned) {
                    if (numericValue < 0) {
                        numericValue += moduloValue;
                    } else if (-0 === numericValue) {
                        return 0;
                    }
                }
            }

            return numericValue;
        };
    }

    // 导出类型转换器
    module.exports = typeConverters;

    // 基本类型转换器
    typeConverters.void = function() {};

    typeConverters.boolean = function(value) {
        return !!value;
    };

    // 整数类型转换器
    typeConverters.byte = createIntegerConverter(8, {unsigned: false});
    typeConverters.octet = createIntegerConverter(8, {unsigned: true});
    typeConverters.short = createIntegerConverter(16, {unsigned: false});
    typeConverters["unsigned short"] = createIntegerConverter(16, {unsigned: true});
    typeConverters.long = createIntegerConverter(32, {unsigned: false});
    typeConverters["unsigned long"] = createIntegerConverter(32, {unsigned: true});
    typeConverters["long long"] = createIntegerConverter(32, {unsigned: false, moduloBitLength: 64});
    typeConverters["unsigned long long"] = createIntegerConverter(32, {unsigned: true, moduloBitLength: 64});

    // 浮点数类型转换器
    typeConverters.double = function(value) {
        var numericValue = +value;
        if (Number.isFinite(numericValue)) {
            return numericValue;
        }
        throw new TypeError("Argument is not a finite floating-point value");
    };

    typeConverters["unrestricted double"] = function(value) {
        var numericValue = +value;
        if (isNaN(numericValue)) {
            throw new TypeError("Argument is NaN");
        }
        return numericValue;
    };

    typeConverters.float = typeConverters.double;
    typeConverters["unrestricted float"] = typeConverters["unrestricted double"];

    // 字符串类型转换器
    typeConverters.DOMString = function(value, options) {
        options = options || {};
        return options.treatNullAsEmptyString && null === value ? "" : String(value);
    };

    typeConverters.ByteString = function(value, options) {
        var stringValue = String(value);
        for (let i = 0; i < stringValue.length; ++i) {
            let codePoint = stringValue.codePointAt(i);
            if (codePoint > 255) {
                throw new TypeError("Argument is not a valid bytestring");
            }
        }
        return stringValue;
    };

    typeConverters.USVString = function(value) {
        var stringValue = String(value);
        var length = stringValue.length;
        var result = [];

        for (let i = 0; i < length; ++i) {
            let charCode = stringValue.charCodeAt(i);

            if (charCode < 55296 || charCode > 57343) {
                result.push(String.fromCodePoint(charCode));
            } else if (!(56320 <= charCode && charCode <= 57343) &&
                       i !== length - 1) {
                let nextCharCode = stringValue.charCodeAt(i + 1);
                if (56320 <= nextCharCode && nextCharCode <= 57343) {
                    result.push(String.fromCodePoint(65536 + 1024 * (1023 & charCode) + (1023 & nextCharCode)));
                    ++i;
                } else {
                    result.push(String.fromCodePoint(65533));
                }
            } else {
                result.push(String.fromCodePoint(65533));
            }
        }

        return result.join("");
    };

    // 日期和正则表达式类型转换器
    typeConverters.Date = function(value, options) {
        if (!(value instanceof Date)) {
            throw new TypeError("Argument is not a Date object");
        }
        if (!isNaN(value)) {
            return value;
        }
    };

    typeConverters.RegExp = function(value, options) {
        return value instanceof RegExp ? value : new RegExp(value);
    };
});

// ============================================================================
// WebIDL 工具模块
// ============================================================================

// WebIDL 工具模块
var webIdlUtilsModule = moduleWrapper((exports, module) => {  // iIe -> webIdlUtilsModule
    // 混入方法 - 将源对象的属性复制到目标对象
    module.exports.mixin = function(target, source) {
        var propertyNames = Object.getOwnPropertyNames(source);
        for (let i = 0; i < propertyNames.length; ++i) {
            Object.defineProperty(target, propertyNames[i],
                Object.getOwnPropertyDescriptor(source, propertyNames[i]));
        }
    };

    // 包装器符号
    module.exports.wrapperSymbol = Symbol("wrapper");

    // 实现符号
    module.exports.implSymbol = Symbol("impl");

    // 获取实现的包装器
    module.exports.wrapperForImpl = function(impl) {
        return impl[module.exports.wrapperSymbol];
    };

    // 获取包装器的实现
    module.exports.implForWrapper = function(wrapper) {
        return wrapper[module.exports.implSymbol];
    };
});

// ============================================================================
// Unicode 字符映射表模块
// ============================================================================

// Unicode 字符映射表模块 - 包含大量的Unicode字符映射数据
var unicodeMappingTableModule = moduleWrapper((exports, module) => {  // aIe -> unicodeMappingTableModule
    // 这是一个巨大的Unicode字符映射表，包含字符的标准化规则
    // 每个条目格式为: [[起始码点, 结束码点], "映射类型", [映射目标]]
    module.exports = [
        // ASCII控制字符和基本拉丁字符
        [[0, 44], "disallowed_STD3_valid"],
        [[45, 46], "valid"],
        [[47, 47], "disallowed_STD3_valid"],
        [[48, 57], "valid"],  // 数字0-9
        [[58, 64], "disallowed_STD3_valid"],

        // 大写字母A-Z映射到小写
        [[65, 65], "mapped", [97]],   // A -> a
        [[66, 66], "mapped", [98]],   // B -> b
        [[67, 67], "mapped", [99]],   // C -> c
        [[68, 68], "mapped", [100]],  // D -> d
        [[69, 69], "mapped", [101]],  // E -> e
        [[70, 70], "mapped", [102]],  // F -> f
        [[71, 71], "mapped", [103]],  // G -> g
        [[72, 72], "mapped", [104]],  // H -> h
        [[73, 73], "mapped", [105]],  // I -> i
        [[74, 74], "mapped", [106]],  // J -> j
        [[75, 75], "mapped", [107]],  // K -> k
        [[76, 76], "mapped", [108]],  // L -> l
        [[77, 77], "mapped", [109]],  // M -> m
        [[78, 78], "mapped", [110]],  // N -> n
        [[79, 79], "mapped", [111]],  // O -> o
        [[80, 80], "mapped", [112]],  // P -> p
        [[81, 81], "mapped", [113]],  // Q -> q
        [[82, 82], "mapped", [114]],  // R -> r
        [[83, 83], "mapped", [115]],  // S -> s
        [[84, 84], "mapped", [116]],  // T -> t
        [[85, 85], "mapped", [117]],  // U -> u
        [[86, 86], "mapped", [118]],  // V -> v
        [[87, 87], "mapped", [119]],  // W -> w
        [[88, 88], "mapped", [120]],  // X -> x
        [[89, 89], "mapped", [121]],  // Y -> y
        [[90, 90], "mapped", [122]],  // Z -> z

        [[91, 96], "disallowed_STD3_valid"],
        [[97, 122], "valid"],  // 小写字母a-z
        [[123, 127], "disallowed_STD3_valid"],
        [[128, 159], "disallowed"],

        // 扩展ASCII和Latin-1补充
        [[160, 160], "disallowed_STD3_mapped", [32]],  // 不间断空格 -> 普通空格
        [[161, 167], "valid", [], "NV8"],
        [[168, 168], "disallowed_STD3_mapped", [32, 776]],  // 分音符
        [[169, 169], "valid", [], "NV8"],  // 版权符号
        [[170, 170], "mapped", [97]],  // 阴性序数指示符 -> a
        [[171, 172], "valid", [], "NV8"],
        [[173, 173], "ignored"],  // 软连字符
        [[174, 174], "valid", [], "NV8"],  // 注册商标符号
        [[175, 175], "disallowed_STD3_mapped", [32, 772]],  // 长音符
        [[176, 177], "valid", [], "NV8"],
        [[178, 178], "mapped", [50]],  // 上标2 -> 2
        [[179, 179], "mapped", [51]],  // 上标3 -> 3
        [[180, 180], "disallowed_STD3_mapped", [32, 769]],  // 锐音符
        [[181, 181], "mapped", [956]],  // 微符号 -> μ
        [[182, 182], "valid", [], "NV8"],  // 段落符号
        [[183, 183], "valid"],  // 中点
        [[184, 184], "disallowed_STD3_mapped", [32, 807]],  // 下加符
        [[185, 185], "mapped", [49]],  // 上标1 -> 1
        [[186, 186], "mapped", [111]],  // 阳性序数指示符 -> o
        [[187, 187], "valid", [], "NV8"],
        [[188, 188], "mapped", [49, 8260, 52]],  // 1/4分数
        [[189, 189], "mapped", [49, 8260, 50]],  // 1/2分数
        [[190, 190], "mapped", [51, 8260, 52]],  // 3/4分数
        [[191, 191], "valid", [], "NV8"],

        // 拉丁字母扩展A区域的大写字母映射到小写
        [[192, 192], "mapped", [224]],  // À -> à
        [[193, 193], "mapped", [225]],  // Á -> á
        [[194, 194], "mapped", [226]],  // Â -> â
        [[195, 195], "mapped", [227]],  // Ã -> ã
        [[196, 196], "mapped", [228]],  // Ä -> ä
        [[197, 197], "mapped", [229]],  // Å -> å
        [[198, 198], "mapped", [230]],  // Æ -> æ
        [[199, 199], "mapped", [231]],  // Ç -> ç
        [[200, 200], "mapped", [232]],  // È -> è
        [[201, 201], "mapped", [233]],  // É -> é
        [[202, 202], "mapped", [234]],  // Ê -> ê
        [[203, 203], "mapped", [235]],  // Ë -> ë
        [[204, 204], "mapped", [236]],  // Ì -> ì
        [[205, 205], "mapped", [237]],  // Í -> í
        [[206, 206], "mapped", [238]],  // Î -> î
        [[207, 207], "mapped", [239]],  // Ï -> ï
        [[208, 208], "mapped", [240]],  // Ð -> ð
        [[209, 209], "mapped", [241]],  // Ñ -> ñ
        [[210, 210], "mapped", [242]],  // Ò -> ò
        [[211, 211], "mapped", [243]],  // Ó -> ó
        [[212, 212], "mapped", [244]],  // Ô -> ô
        [[213, 213], "mapped", [245]],  // Õ -> õ
        [[214, 214], "mapped", [246]],  // Ö -> ö
        [[215, 215], "valid", [], "NV8"],  // 乘号
        [[216, 216], "mapped", [248]],  // Ø -> ø
        [[217, 217], "mapped", [249]],  // Ù -> ù
        [[218, 218], "mapped", [250]],  // Ú -> ú
        [[219, 219], "mapped", [251]],  // Û -> û
        [[220, 220], "mapped", [252]],  // Ü -> ü
        [[221, 221], "mapped", [253]],  // Ý -> ý
        [[222, 222], "mapped", [254]],  // Þ -> þ
        [[223, 223], "deviation", [115, 115]],  // ß -> ss
        [[224, 246], "valid"],  // 小写拉丁字母
        [[247, 247], "valid", [], "NV8"],  // 除号
        [[248, 255], "valid"]  // 更多小写拉丁字母

        // 注意：实际的映射表包含数千个条目，这里只显示了前面的部分
        // 完整的表包含所有Unicode字符的映射规则
    ];
});

// ============================================================================
// 退避算法模块
// ============================================================================

// 退避算法模块 - 实现指数退避重试机制
var backoffAlgorithmModule = moduleWrapper((exports, module) => {  // dbt -> backoffAlgorithmModule
    // TypeScript生成器运行时辅助函数
    var __generator = function(thisArg, body) {
        var _ = {
            label: 0,
            sent: function() {
                if (trys[0] & 1) throw trys[1];
                return trys[1];
            },
            trys: [],
            ops: []
        };
        var generator = {
            next: step(0),
            "throw": step(1),
            "return": step(2)
        };

        // 添加迭代器支持
        if ("function" == typeof Symbol) {
            generator[Symbol.iterator] = function() {
                return this;
            };
        }

        return generator;

        function step(op) {
            return function(arg) {
                var value = [op, arg];
                if (executing) throw new TypeError("Generator is already executing.");

                while (state) {
                    try {
                        if (executing = 1,
                            yielded && (result = 2 & value[0] ? yielded["return"] :
                                value[0] ? yielded["throw"] || ((result = yielded["return"]) && result.call(yielded), 0) :
                                yielded.next) &&
                            !(result = result.call(yielded, value[1])).done) {
                            return result;
                        }

                        yielded = 0;
                        if (result) value = [2 & value[0], result.value];

                        switch (value[0]) {
                            case 0:
                            case 1:
                                result = value;
                                break;
                            case 4:
                                state.label++;
                                return {
                                    value: value[1],
                                    done: false
                                };
                            case 5:
                                state.label++;
                                yielded = value[1];
                                value = [0];
                                continue;
                            case 7:
                                value = state.ops.pop();
                                state.trys.pop();
                                continue;
                            default:
                                if (!(result = state.trys.length > 0 && state.trys[state.trys.length - 1]) &&
                                    (6 === value[0] || 2 === value[0])) {
                                    state = 0;
                                    continue;
                                }
                                if (3 === value[0] && (!result || value[1] > result[0] && value[1] < result[3])) {
                                    state.label = value[1];
                                } else if (6 === value[0] && state.label < result[1]) {
                                    state.label = result[1];
                                    result = value;
                                } else {
                                    if (!(result && state.label < result[2])) {
                                        if (result[2]) state.ops.pop();
                                        state.trys.pop();
                                        continue;
                                    }
                                    state.label = result[2];
                                    state.ops.push(value);
                                }
                        }
                        value = body.call(thisArg, state);
                    } catch (e) {
                        value = [6, e];
                        yielded = 0;
                    } finally {
                        executing = result = 0;
                    }
                }

                if (5 & value[0]) throw value[1];
                return {
                    value: value[0] ? value[1] : void 0,
                    done: true
                };
            };
        }

        var executing, yielded, result, state = _;
    };

    // 导出模块
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var optionsSanitizerModule = optionsSanitizerModule(),
        delayFactoryModule = delayFactoryModule();

    // 退避函数 - 主要的退避重试接口
    exports.backOff = function(request, options) {
        if (options === void 0) options = {};

        return new Promise(function(resolve, reject) {
            var sanitizedOptions = optionsSanitizerModule.getSanitizedOptions(options);
            var backoffInstance = new BackoffClass(request, sanitizedOptions);

            backoffInstance.execute().then(resolve).catch(reject);
        });
    };

    // 退避类 - 实现退避逻辑
    var BackoffClass = function(request, options) {
        this.request = request;
        this.options = options;
        this.attemptNumber = 0;
    };

    // 执行退避重试
    BackoffClass.prototype.execute = function() {
        var self = this;
        return new Promise(function(resolve, reject) {
            function attemptRequest() {
                if (self.attemptLimitReached) {
                    reject(new Error("Maximum retry attempts reached"));
                    return;
                }

                // 应用延迟
                self.applyDelay().then(function() {
                    // 执行请求
                    return self.request();
                }).then(function(result) {
                    resolve(result);
                }).catch(function(error) {
                    self.attemptNumber++;

                    // 检查是否应该重试
                    return self.options.retry(error, self.attemptNumber);
                }).then(function(shouldRetry) {
                    if (shouldRetry && !self.attemptLimitReached) {
                        attemptRequest();
                    } else {
                        reject(error);
                    }
                });
            }

            attemptRequest();
        });
    };

    // 检查是否达到尝试限制
    Object.defineProperty(BackoffClass.prototype, "attemptLimitReached", {
        get: function() {
            return this.attemptNumber >= this.options.numOfAttempts;
        },
        enumerable: true,
        configurable: true
    });

    // 应用延迟
    BackoffClass.prototype.applyDelay = function() {
        var self = this;
        return new Promise(function(resolve) {
            var delay = delayFactoryModule.DelayFactory(self.options, self.attemptNumber);
            delay.apply().then(resolve);
        });
    };
});

// ============================================================================
// Unicode 字符识别模块
// ============================================================================

// Unicode 字符识别模块
var unicodeCharacterModule = moduleWrapper((exports, module) => {  // fbt -> unicodeCharacterModule
    // 导出Unicode字符识别正则表达式
    module.exports.Space_Separator = /[\u1680\u2000-\u200A\u202F\u205F\u3000]/;
    module.exports.ID_Start = /[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE83\uDE86-\uDE89\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]/;
    module.exports.ID_Continue = /[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u09FC\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\uAA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D00-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF9\u1D00-\u1DF9\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDE00-\uDE3E\uDE47\uDE50-\uDE83\uDE86-\uDE99\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD47\uDD50-\uDD59]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4A\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/;
});
